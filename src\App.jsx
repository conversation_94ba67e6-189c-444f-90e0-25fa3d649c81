import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON>out<PERSON> as Router } from 'react-router-dom'
import { AuthProvider } from './contexts/AuthContext'
import { ThemeProvider } from './contexts/ThemeContext'
import AppRouter from './router'

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <Router>
          <div className="min-h-screen bg-base-100 text-base-content">
            <AppRouter />
          </div>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  )
}

export default App
