// Authentication utility functions

export const ROLES = {
  ADMIN: 'admin',
  PROFESSOR: 'professor',
  STUDENT: 'student'
}

export const STORAGE_KEYS = {
  TOKEN: 'lms_token',
  USER: 'lms_user',
  THEME: 'lms_theme'
}

// Token management
export const getToken = () => {
  try {
    return localStorage.getItem(STORAGE_KEYS.TOKEN)
  } catch (error) {
    console.error('Error getting token:', error)
    return null
  }
}

export const setToken = (token) => {
  try {
    localStorage.setItem(STORAGE_KEYS.TOKEN, token)
  } catch (error) {
    console.error('Error setting token:', error)
  }
}

export const removeToken = () => {
  try {
    localStorage.removeItem(STORAGE_KEYS.TOKEN)
  } catch (error) {
    console.error('Error removing token:', error)
  }
}

// User management
export const getUser = () => {
  try {
    const user = localStorage.getItem(STORAGE_KEYS.USER)
    return user ? JSON.parse(user) : null
  } catch (error) {
    console.error('Error getting user:', error)
    return null
  }
}

export const setUser = (user) => {
  try {
    localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user))
  } catch (error) {
    console.error('Error setting user:', error)
  }
}

export const removeUser = () => {
  try {
    localStorage.removeItem(STORAGE_KEYS.USER)
  } catch (error) {
    console.error('Error removing user:', error)
  }
}

// Authentication status
export const isAuthenticated = () => {
  const token = getToken()
  const user = getUser()
  return !!(token && user)
}

// Role checking
export const hasRole = (requiredRole) => {
  const user = getUser()
  return user && user.role === requiredRole
}

export const isAdmin = () => hasRole(ROLES.ADMIN)
export const isProfessor = () => hasRole(ROLES.PROFESSOR)
export const isStudent = () => hasRole(ROLES.STUDENT)

// Logout function
export const logout = () => {
  removeToken()
  removeUser()
  // Redirect to login page
  window.location.href = '/login'
}

// Mock JWT decode (in real app, use a proper JWT library)
export const decodeToken = (token) => {
  try {
    const base64Url = token.split('.')[1]
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    )
    return JSON.parse(jsonPayload)
  } catch (error) {
    console.error('Error decoding token:', error)
    return null
  }
}

// Check if token is expired
export const isTokenExpired = (token) => {
  if (!token) return true
  
  const decoded = decodeToken(token)
  if (!decoded || !decoded.exp) return true
  
  const currentTime = Date.now() / 1000
  return decoded.exp < currentTime
}

// Get user role from token or user object
export const getUserRole = () => {
  const user = getUser()
  if (user && user.role) return user.role
  
  const token = getToken()
  if (token) {
    const decoded = decodeToken(token)
    return decoded?.role || null
  }
  
  return null
}

// Format user display name
export const getUserDisplayName = (user) => {
  if (!user) return 'Guest'
  return user.firstName && user.lastName 
    ? `${user.firstName} ${user.lastName}`
    : user.email || user.username || 'User'
}

// Generate avatar URL or initials
export const getUserAvatar = (user) => {
  if (!user) return null
  
  if (user.avatar) return user.avatar
  
  // Generate initials
  const name = getUserDisplayName(user)
  const initials = name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .join('')
    .slice(0, 2)
  
  return initials
}

// Theme management
export const getTheme = () => {
  try {
    return localStorage.getItem(STORAGE_KEYS.THEME) || 'light'
  } catch (error) {
    console.error('Error getting theme:', error)
    return 'light'
  }
}

export const setTheme = (theme) => {
  try {
    localStorage.setItem(STORAGE_KEYS.THEME, theme)
    document.documentElement.setAttribute('data-theme', theme)
  } catch (error) {
    console.error('Error setting theme:', error)
  }
}

// Initialize theme on app load
export const initializeTheme = () => {
  const savedTheme = getTheme()
  setTheme(savedTheme)
}
