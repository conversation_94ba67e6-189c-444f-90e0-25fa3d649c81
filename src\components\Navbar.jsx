import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useAuth } from '../contexts/AuthContext'
import { useTheme } from '../contexts/ThemeContext'

const Navbar = ({ onSidebarToggle }) => {
  const { user, logout, getDisplayName, isAuthenticated } = useAuth()
  const { currentTheme, toggleDarkMode, isDarkMode, getAllThemes, setTheme } = useTheme()
  const [showThemeDropdown, setShowThemeDropdown] = useState(false)
  const [showProfileDropdown, setShowProfileDropdown] = useState(false)

  const handleLogout = async () => {
    await logout()
  }

  const getUserInitials = () => {
    const name = getDisplayName()
    return name
      .split(' ')
      .map(word => word.charAt(0).toUpperCase())
      .join('')
      .slice(0, 2)
  }

  const getRoleColor = (role) => {
    switch (role) {
      case 'admin':
        return 'badge-error'
      case 'professor':
        return 'badge-warning'
      case 'student':
        return 'badge-info'
      default:
        return 'badge-neutral'
    }
  }

  const navVariants = {
    hidden: { y: -100, opacity: 0 },
    visible: { 
      y: 0, 
      opacity: 1,
      transition: { 
        type: "spring", 
        stiffness: 100, 
        damping: 20 
      }
    }
  }

  const dropdownVariants = {
    hidden: { 
      opacity: 0, 
      scale: 0.95, 
      y: -10 
    },
    visible: { 
      opacity: 1, 
      scale: 1, 
      y: 0,
      transition: { 
        type: "spring", 
        stiffness: 300, 
        damping: 30 
      }
    },
    exit: { 
      opacity: 0, 
      scale: 0.95, 
      y: -10,
      transition: { duration: 0.2 }
    }
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <motion.div 
      className="navbar bg-base-100 shadow-lg border-b border-base-300 sticky top-0 z-50 backdrop-blur-md"
      variants={navVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Left side */}
      <div className="navbar-start">
        {/* Sidebar toggle button */}
        <motion.button
          className="btn btn-ghost btn-circle lg:hidden"
          onClick={onSidebarToggle}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </motion.button>

        {/* Logo and title */}
        <motion.div 
          className="flex items-center space-x-3"
          whileHover={{ scale: 1.02 }}
        >
          <div className="avatar">
            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center">
              <span className="text-white font-bold text-lg">LMS</span>
            </div>
          </div>
          <div className="hidden sm:block">
            <h1 className="text-xl font-bold gradient-text">Learning Management System</h1>
            <p className="text-xs text-base-content/60">
              {user?.role === 'admin' && 'Administrator Panel'}
              {user?.role === 'professor' && 'Professor Dashboard'}
              {user?.role === 'student' && 'Student Portal'}
            </p>
          </div>
        </motion.div>
      </div>

      {/* Center - Search (for larger screens) */}
      <div className="navbar-center hidden lg:flex">
        <div className="form-control">
          <motion.div 
            className="input-group"
            whileFocus={{ scale: 1.02 }}
          >
            <input 
              type="text" 
              placeholder="Search courses, quizzes..." 
              className="input input-bordered w-64" 
            />
            <button className="btn btn-square btn-primary">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button>
          </motion.div>
        </div>
      </div>

      {/* Right side */}
      <div className="navbar-end space-x-2">
        {/* Theme selector */}
        <div className="dropdown dropdown-end">
          <motion.button
            className="btn btn-ghost btn-circle"
            onClick={() => setShowThemeDropdown(!showThemeDropdown)}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            {isDarkMode ? (
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
              </svg>
            ) : (
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
              </svg>
            )}
          </motion.button>
          
          <AnimatePresence>
            {showThemeDropdown && (
              <motion.div
                className="dropdown-content z-[1] menu p-2 shadow-lg bg-base-100 rounded-box w-52 border border-base-300 max-h-96 overflow-y-auto"
                variants={dropdownVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
              >
                <div className="menu-title">
                  <span>Choose Theme</span>
                </div>
                <div className="divider my-1"></div>
                
                {/* Quick toggle */}
                <li>
                  <button onClick={toggleDarkMode} className="justify-between">
                    <span>Toggle Dark Mode</span>
                    <div className={`toggle toggle-sm ${isDarkMode ? 'toggle-primary' : ''}`}>
                      <input type="checkbox" checked={isDarkMode} readOnly />
                    </div>
                  </button>
                </li>
                
                <div className="divider my-1"></div>
                
                {/* Theme options */}
                {getAllThemes().slice(0, 8).map((theme) => (
                  <li key={theme}>
                    <button
                      onClick={() => {
                        setTheme(theme)
                        setShowThemeDropdown(false)
                      }}
                      className={`justify-between ${currentTheme === theme ? 'active' : ''}`}
                    >
                      <span className="capitalize">{theme}</span>
                      {currentTheme === theme && (
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </button>
                  </li>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Notifications */}
        <motion.button
          className="btn btn-ghost btn-circle"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <div className="indicator">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM10.97 4.97a.235.235 0 0 0-.02 0L9 7h6l-1.95-2.03a.235.235 0 0 0-.02 0L12 4l-1.03.97zM18 8h-6l2 2h4V8zM6 8v2h4l2-2H6zM4 12v6h6v-6H4z" />
            </svg>
            <span className="badge badge-xs badge-primary indicator-item">3</span>
          </div>
        </motion.button>

        {/* User profile dropdown */}
        <div className="dropdown dropdown-end">
          <motion.div
            className="avatar cursor-pointer"
            onClick={() => setShowProfileDropdown(!showProfileDropdown)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center text-white font-semibold">
              {user?.avatar ? (
                <img src={user.avatar} alt="Avatar" className="rounded-full" />
              ) : (
                getUserInitials()
              )}
            </div>
          </motion.div>
          
          <AnimatePresence>
            {showProfileDropdown && (
              <motion.div
                className="dropdown-content z-[1] menu p-2 shadow-lg bg-base-100 rounded-box w-64 border border-base-300"
                variants={dropdownVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
              >
                {/* User info */}
                <div className="px-4 py-3 border-b border-base-300">
                  <div className="font-semibold">{getDisplayName()}</div>
                  <div className="text-sm text-base-content/60">{user?.email}</div>
                  <div className="mt-2">
                    <span className={`badge badge-sm ${getRoleColor(user?.role)}`}>
                      {user?.role?.toUpperCase()}
                    </span>
                  </div>
                </div>
                
                {/* Menu items */}
                <li>
                  <a className="justify-between">
                    Profile Settings
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </a>
                </li>
                <li>
                  <a className="justify-between">
                    Preferences
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </a>
                </li>
                <div className="divider my-1"></div>
                <li>
                  <button onClick={handleLogout} className="text-error justify-between">
                    Logout
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                  </button>
                </li>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </motion.div>
  )
}

export default Navbar
