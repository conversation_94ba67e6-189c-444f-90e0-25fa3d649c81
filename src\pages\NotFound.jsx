import React from 'react'
import { motion } from 'framer-motion'
import { Link, useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import AnimatedBackground from '../components/AnimatedBackground'

const NotFound = () => {
  const { isAuthenticated, user } = useAuth()
  const navigate = useNavigate()

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    }
  }

  const floatingVariants = {
    animate: {
      y: [-10, 10, -10],
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  }

  const getDashboardPath = () => {
    if (!isAuthenticated || !user) return '/login'
    return `/${user.role}`
  }

  return (
    <AnimatedBackground type="particles" className="min-h-screen flex items-center justify-center p-4">
      <motion.div
        className="text-center max-w-2xl mx-auto relative z-10"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* 404 Animation */}
        <motion.div
          className="mb-8"
          variants={itemVariants}
        >
          <motion.div
            className="text-9xl font-bold gradient-text mb-4"
            variants={floatingVariants}
            animate="animate"
          >
            404
          </motion.div>
          <motion.div
            className="text-6xl mb-4"
            animate={{
              rotate: [0, 10, -10, 0],
              transition: {
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }
            }}
          >
            🔍
          </motion.div>
        </motion.div>

        {/* Error Message */}
        <motion.div variants={itemVariants} className="mb-8">
          <h1 className="text-4xl font-bold mb-4">Page Not Found</h1>
          <p className="text-xl text-base-content/70 mb-2">
            Oops! The page you're looking for doesn't exist.
          </p>
          <p className="text-base-content/60">
            It might have been moved, deleted, or you entered the wrong URL.
          </p>
        </motion.div>

        {/* Suggestions */}
        <motion.div
          variants={itemVariants}
          className="card bg-base-100/90 backdrop-blur-md shadow-xl border border-base-300 mb-8 relative z-10"
        >
          <div className="card-body">
            <h3 className="card-title text-lg mb-4">What can you do?</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                  </svg>
                </div>
                <div>
                  <h4 className="font-semibold">Go to Dashboard</h4>
                  <p className="text-sm text-base-content/70">Return to your main dashboard</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-secondary/20 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <div>
                  <h4 className="font-semibold">Search</h4>
                  <p className="text-sm text-base-content/70">Look for what you need</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-accent/20 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                <div>
                  <h4 className="font-semibold">Browse Courses</h4>
                  <p className="text-sm text-base-content/70">Explore available courses</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-info/20 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h4 className="font-semibold">Get Help</h4>
                  <p className="text-sm text-base-content/70">Contact support team</p>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          variants={itemVariants}
          className="flex flex-col sm:flex-row gap-4 justify-center"
        >
          <motion.button
            className="btn btn-primary btn-lg"
            onClick={() => navigate(getDashboardPath())}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
            </svg>
            {isAuthenticated ? 'Go to Dashboard' : 'Go to Login'}
          </motion.button>
          
          <motion.button
            className="btn btn-outline btn-lg"
            onClick={() => navigate(-1)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Go Back
          </motion.button>
        </motion.div>

        {/* Quick Links */}
        {isAuthenticated && (
          <motion.div
            variants={itemVariants}
            className="mt-8 pt-8 border-t border-base-300"
          >
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <div className="flex flex-wrap justify-center gap-2">
              {user?.role === 'student' && (
                <>
                  <Link to="/student/courses" className="btn btn-ghost btn-sm">
                    Browse Courses
                  </Link>
                  <Link to="/student/quiz" className="btn btn-ghost btn-sm">
                    Take Quiz
                  </Link>
                  <Link to="/student/progress" className="btn btn-ghost btn-sm">
                    My Progress
                  </Link>
                </>
              )}
              
              {user?.role === 'professor' && (
                <>
                  <Link to="/professor/courses" className="btn btn-ghost btn-sm">
                    My Courses
                  </Link>
                  <Link to="/professor/quiz/create" className="btn btn-ghost btn-sm">
                    Create Quiz
                  </Link>
                  <Link to="/professor/analytics" className="btn btn-ghost btn-sm">
                    Analytics
                  </Link>
                </>
              )}
              
              {user?.role === 'admin' && (
                <>
                  <Link to="/admin/users" className="btn btn-ghost btn-sm">
                    User Management
                  </Link>
                  <Link to="/admin/analytics" className="btn btn-ghost btn-sm">
                    System Analytics
                  </Link>
                  <Link to="/admin/settings" className="btn btn-ghost btn-sm">
                    Settings
                  </Link>
                </>
              )}
            </div>
          </motion.div>
        )}

        {/* Footer */}
        <motion.div
          variants={itemVariants}
          className="mt-12 text-sm text-base-content/60"
        >
          <p>If you believe this is an error, please contact our support team.</p>
          <p className="mt-2">
            <Link to="/contact" className="link link-primary">
              Contact Support
            </Link>
            {' • '}
            <Link to="/help" className="link link-primary">
              Help Center
            </Link>
          </p>
        </motion.div>
      </motion.div>
    </AnimatedBackground>
  )
}

export default NotFound
