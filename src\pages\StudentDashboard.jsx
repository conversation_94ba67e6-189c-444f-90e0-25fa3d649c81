import React, { useState, useEffect } from 'react'
import { Routes, Route } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import Navbar from '../components/Navbar'
import Sidebar from '../components/Sidebar'
import ScoreChart from '../components/ScoreChart'
import QuizCard, { QuizGrid } from '../components/QuizCard'
import FlashcardViewer from '../components/FlashcardViewer'
import AnimatedBackground from '../components/AnimatedBackground'

const StudentDashboard = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [enrolledCourses, setEnrolledCourses] = useState([])
  const [availableQuizzes, setAvailableQuizzes] = useState([])
  const [recommendations, setRecommendations] = useState([])
  const [progress, setProgress] = useState({})
  const [loading, setLoading] = useState(true)
  const [showFlashcards, setShowFlashcards] = useState(false)

  const { user } = useAuth()

  useEffect(() => {
    // Simulate loading data
    setTimeout(() => {
      setEnrolledCourses([
        { 
          id: 1, 
          title: 'Introduction to Computer Science', 
          instructor: 'Dr. <PERSON>',
          progress: 75, 
          nextDeadline: '2024-01-20',
          status: 'active',
          thumbnail: '💻'
        },
        { 
          id: 2, 
          title: 'Data Structures & Algorithms', 
          instructor: 'Prof. Johnson',
          progress: 45, 
          nextDeadline: '2024-01-18',
          status: 'active',
          thumbnail: '🔗'
        },
        { 
          id: 3, 
          title: 'Web Development Fundamentals', 
          instructor: 'Dr. Wilson',
          progress: 90, 
          nextDeadline: '2024-01-25',
          status: 'active',
          thumbnail: '🌐'
        }
      ])
      
      setAvailableQuizzes([
        { 
          id: 1, 
          title: 'JavaScript Fundamentals', 
          description: 'Test your knowledge of JavaScript basics',
          subject: 'Programming',
          difficulty: 'easy',
          questionCount: 15,
          duration: 30,
          status: 'not-started',
          dueDate: '2024-01-20'
        },
        { 
          id: 2, 
          title: 'Data Structures Quiz', 
          description: 'Arrays, linked lists, and basic algorithms',
          subject: 'Computer Science',
          difficulty: 'medium',
          questionCount: 20,
          duration: 45,
          status: 'completed',
          score: 85,
          completedAt: '2024-01-10'
        },
        { 
          id: 3, 
          title: 'HTML & CSS Basics', 
          description: 'Web development fundamentals',
          subject: 'Web Development',
          difficulty: 'easy',
          questionCount: 12,
          duration: 25,
          status: 'in-progress',
          progress: 60
        }
      ])

      setRecommendations([
        { id: 1, title: 'Advanced JavaScript', type: 'course', reason: 'Based on your JavaScript quiz performance' },
        { id: 2, title: 'React Fundamentals', type: 'course', reason: 'Popular among students in your program' },
        { id: 3, title: 'Algorithm Practice', type: 'quiz', reason: 'Improve your data structures knowledge' }
      ])

      setProgress({
        overallGPA: 3.7,
        completedCourses: 8,
        totalCredits: 24,
        currentSemester: 'Spring 2024'
      })

      setLoading(false)
    }, 1000)
  }, [])

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    }
  }

  // Mock data for charts
  const performanceData = [
    { name: 'Week 1', score: 78 },
    { name: 'Week 2', score: 82 },
    { name: 'Week 3', score: 75 },
    { name: 'Week 4', score: 88 },
    { name: 'Week 5', score: 85 },
    { name: 'Week 6', score: 92 }
  ]

  const subjectScores = [
    { name: 'Programming', score: 85 },
    { name: 'Mathematics', score: 78 },
    { name: 'Web Dev', score: 92 },
    { name: 'Algorithms', score: 80 }
  ]

  // Mock flashcards data
  const flashcards = [
    {
      id: 1,
      question: 'What is a variable in programming?',
      answer: 'A variable is a storage location with an associated name that contains data.',
      category: 'Programming Basics'
    },
    {
      id: 2,
      question: 'What does HTML stand for?',
      answer: 'HyperText Markup Language',
      category: 'Web Development'
    },
    {
      id: 3,
      question: 'What is the time complexity of binary search?',
      answer: 'O(log n)',
      category: 'Algorithms'
    }
  ]

  const handleStartQuiz = (quiz) => {
    console.log('Starting quiz:', quiz)
    // Navigate to quiz taking interface
  }

  const handleEnrollCourse = (courseId) => {
    console.log('Enrolling in course:', courseId)
  }

  const MainDashboard = () => (
    <AnimatedBackground type="net" className="min-h-screen">
      <motion.div
        className="space-y-6 relative z-10"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Welcome Section */}
        <motion.div variants={itemVariants}>
          <div className="hero bg-gradient-to-r from-accent to-primary text-accent-content rounded-2xl">
            <div className="hero-content text-center py-12">
              <div className="max-w-md">
                <motion.h1 
                  className="text-4xl font-bold mb-4"
                  animate={{ 
                    backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
                  }}
                  transition={{ duration: 3, repeat: Infinity }}
                >
                  Welcome back, {user?.firstName || 'Student'}! 🎓
                </motion.h1>
                <p className="text-lg opacity-90">
                  Continue your learning journey and achieve your goals
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Progress Overview */}
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
          variants={containerVariants}
        >
          {[
            { title: 'Overall GPA', value: progress.overallGPA, icon: '📊', color: 'from-blue-500 to-blue-600' },
            { title: 'Completed Courses', value: progress.completedCourses, icon: '✅', color: 'from-green-500 to-green-600' },
            { title: 'Total Credits', value: progress.totalCredits, icon: '🎯', color: 'from-purple-500 to-purple-600' },
            { title: 'Current Semester', value: progress.currentSemester, icon: '📅', color: 'from-orange-500 to-orange-600' }
          ].map((stat, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              whileHover={{ scale: 1.05, y: -5 }}
              className="card bg-base-100/90 backdrop-blur-md shadow-lg border border-base-300 overflow-hidden"
            >
              <div className={`h-2 bg-gradient-to-r ${stat.color}`}></div>
              <div className="card-body p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-base-content/60 text-sm font-medium">{stat.title}</p>
                    <p className="text-2xl font-bold">{stat.value}</p>
                  </div>
                  <div className="text-3xl">{stat.icon}</div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Enrolled Courses */}
        <motion.div variants={itemVariants}>
          <div className="card bg-base-100/90 backdrop-blur-md shadow-lg border border-base-300">
            <div className="card-body">
              <div className="flex items-center justify-between mb-4">
                <h3 className="card-title">My Courses</h3>
                <motion.button 
                  className="btn btn-primary btn-sm"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Browse All Courses
                </motion.button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {enrolledCourses.map((course, index) => (
                  <motion.div
                    key={course.id}
                    className="card bg-base-200/80 backdrop-blur-sm shadow-md border border-base-300"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    whileHover={{ scale: 1.02, y: -2 }}
                  >
                    <div className="card-body p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="text-2xl">{course.thumbnail}</div>
                        <span className={`badge ${course.status === 'active' ? 'badge-success' : 'badge-warning'}`}>
                          {course.status}
                        </span>
                      </div>
                      <h4 className="font-semibold text-sm mb-1">{course.title}</h4>
                      <p className="text-xs text-base-content/60 mb-2">{course.instructor}</p>
                      <div className="mb-3">
                        <div className="flex items-center justify-between text-xs mb-1">
                          <span>Progress</span>
                          <span>{course.progress}%</span>
                        </div>
                        <progress className="progress progress-primary w-full h-2" value={course.progress} max="100"></progress>
                      </div>
                      <div className="text-xs text-base-content/60">
                        Next deadline: {course.nextDeadline}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </motion.div>

        {/* Performance Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <motion.div variants={itemVariants}>
            <div className="bg-base-100/90 backdrop-blur-md rounded-2xl">
              <ScoreChart
                data={performanceData}
                type="line"
                title="Performance Trends"
                height={300}
              />
            </div>
          </motion.div>
          <motion.div variants={itemVariants}>
            <div className="bg-base-100/90 backdrop-blur-md rounded-2xl">
              <ScoreChart
                data={subjectScores}
                type="bar"
                title="Subject Scores"
                height={300}
              />
            </div>
          </motion.div>
        </div>

        {/* Available Quizzes */}
        <motion.div variants={itemVariants}>
          <div className="card bg-base-100/90 backdrop-blur-md shadow-lg border border-base-300">
            <div className="card-body">
              <div className="flex items-center justify-between mb-4">
                <h3 className="card-title">Available Quizzes</h3>
                <motion.button 
                  className="btn btn-secondary btn-sm"
                  onClick={() => setShowFlashcards(true)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Study with Flashcards
                </motion.button>
              </div>
              <QuizGrid
                quizzes={availableQuizzes}
                onStart={handleStartQuiz}
                loading={loading}
                emptyMessage="No quizzes available at the moment."
              />
            </div>
          </div>
        </motion.div>

        {/* Personalized Recommendations */}
        <motion.div variants={itemVariants}>
          <div className="card bg-base-100/90 backdrop-blur-md shadow-lg border border-base-300">
            <div className="card-body">
              <h3 className="card-title mb-4">Recommended for You</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {recommendations.map((rec, index) => (
                  <motion.div
                    key={rec.id}
                    className="card bg-gradient-to-br from-primary/10 to-secondary/10 border border-primary/20"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                  >
                    <div className="card-body p-4">
                      <div className="flex items-center mb-2">
                        <span className="text-lg mr-2">
                          {rec.type === 'course' ? '📚' : '📝'}
                        </span>
                        <span className={`badge badge-sm ${rec.type === 'course' ? 'badge-primary' : 'badge-secondary'}`}>
                          {rec.type}
                        </span>
                      </div>
                      <h4 className="font-semibold text-sm mb-2">{rec.title}</h4>
                      <p className="text-xs text-base-content/70 mb-3">{rec.reason}</p>
                      <motion.button 
                        className="btn btn-outline btn-xs w-full"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        {rec.type === 'course' ? 'Enroll' : 'Take Quiz'}
                      </motion.button>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatedBackground>
  )

  return (
    <div className="min-h-screen bg-base-100">
      <Navbar onSidebarToggle={() => setSidebarOpen(!sidebarOpen)} />
      
      <div className="flex">
        <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />
        
        <main className="flex-1 p-6 lg:ml-0">
          <Routes>
            <Route path="/" element={<MainDashboard />} />
            <Route path="/courses" element={<div>Browse Courses (Coming Soon)</div>} />
            <Route path="/quiz" element={<div>Take Quiz (Coming Soon)</div>} />
            <Route path="/progress" element={<div>My Progress (Coming Soon)</div>} />
            <Route path="/flashcards" element={<div>Flashcards (Coming Soon)</div>} />
          </Routes>
        </main>
      </div>

      {/* Flashcards Modal */}
      <AnimatePresence>
        {showFlashcards && (
          <motion.div
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowFlashcards(false)}
          >
            <motion.div
              className="bg-base-100 rounded-2xl p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto"
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.5, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-bold">Study Flashcards</h3>
                <button 
                  className="btn btn-ghost btn-circle"
                  onClick={() => setShowFlashcards(false)}
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <FlashcardViewer
                flashcards={flashcards}
                onComplete={() => {
                  alert('Great job! You\'ve completed all flashcards.')
                  setShowFlashcards(false)
                }}
                autoPlay={false}
                showProgress={true}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default StudentDashboard
