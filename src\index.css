@import 'tailwindcss';

/* Custom CSS Variables for Theme Support */
:root {
  --animation-duration: 0.3s;
  --border-radius: 0.5rem;
  --shadow-color: rgba(0, 0, 0, 0.1);
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  color: hsl(var(--bc));
  background-color: hsl(var(--b1));
  overflow-x: hidden;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--b2));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--bc) / 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--bc) / 0.5);
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn var(--animation-duration) ease-in-out;
}

.animate-slide-up {
  animation: slideUp var(--animation-duration) ease-out;
}

.animate-scale-in {
  animation: scaleIn var(--animation-duration) ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Custom component styles */
.glass-effect {
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-text {
  background: linear-gradient(135deg, hsl(var(--p)), hsl(var(--s)));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Loading spinner */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid hsl(var(--b3));
  border-top: 4px solid hsl(var(--p));
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Simple background utilities using DaisyUI colors */
.bg-pattern-dots {
  background-image: radial-gradient(circle, hsl(var(--bc) / 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.bg-pattern-grid {
  background-image:
    linear-gradient(hsl(var(--bc) / 0.1) 1px, transparent 1px),
    linear-gradient(90deg, hsl(var(--bc) / 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }
}

@media (min-width: 769px) {
  .desktop-hidden {
    display: none;
  }
}
