import React, { createContext, useContext, useReducer, useEffect } from 'react'
import { getTheme, setTheme as setStoredTheme } from '../utils/auth'

// Available themes
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  CUPCAKE: 'cupcake',
  CORPORATE: 'corporate',
  SYNTHWAVE: 'synthwave',
  RETRO: 'retro',
  CYBERPUNK: 'cyberpunk',
  VALENTINE: 'valentine',
  HALLOWEEN: 'halloween',
  GARDEN: 'garden',
  FOREST: 'forest',
  AQUA: 'aqua',
  LOFI: 'lofi',
  PASTEL: 'pastel',
  FANTASY: 'fantasy',
  WIREFRAME: 'wireframe',
  BLACK: 'black',
  LUXURY: 'luxury',
  DRACULA: 'dracula',
  CMYK: 'cmyk',
  AUTUMN: 'autumn',
  BUSINESS: 'business',
  ACID: 'acid',
  LEMONADE: 'lemonade',
  NIGHT: 'night',
  COFFEE: 'coffee',
  WINTER: 'winter',
}

// Theme categories for better organization
export const THEME_CATEGORIES = {
  BASIC: {
    name: 'Basic',
    themes: [THEMES.LIGHT, THEMES.DARK, THEMES.CORPORATE, THEMES.BUSINESS]
  },
  COLORFUL: {
    name: 'Colorful',
    themes: [THEMES.CUPCAKE, THEMES.VALENTINE, THEMES.GARDEN, THEMES.AQUA, THEMES.PASTEL]
  },
  DARK: {
    name: 'Dark',
    themes: [THEMES.SYNTHWAVE, THEMES.HALLOWEEN, THEMES.FOREST, THEMES.BLACK, THEMES.LUXURY, THEMES.DRACULA, THEMES.NIGHT]
  },
  RETRO: {
    name: 'Retro',
    themes: [THEMES.RETRO, THEMES.CYBERPUNK, THEMES.LOFI, THEMES.AUTUMN, THEMES.COFFEE]
  },
  SPECIAL: {
    name: 'Special',
    themes: [THEMES.FANTASY, THEMES.WIREFRAME, THEMES.CMYK, THEMES.ACID, THEMES.LEMONADE, THEMES.WINTER]
  }
}

// Initial state
const initialState = {
  currentTheme: THEMES.LIGHT,
  isDarkMode: false,
  isSystemTheme: false,
}

// Action types
const THEME_ACTIONS = {
  SET_THEME: 'SET_THEME',
  TOGGLE_DARK_MODE: 'TOGGLE_DARK_MODE',
  SET_SYSTEM_THEME: 'SET_SYSTEM_THEME',
}

// Reducer
const themeReducer = (state, action) => {
  switch (action.type) {
    case THEME_ACTIONS.SET_THEME:
      return {
        ...state,
        currentTheme: action.payload,
        isDarkMode: action.payload === THEMES.DARK || isDarkTheme(action.payload),
        isSystemTheme: false,
      }
    
    case THEME_ACTIONS.TOGGLE_DARK_MODE:
      const newTheme = state.isDarkMode ? THEMES.LIGHT : THEMES.DARK
      return {
        ...state,
        currentTheme: newTheme,
        isDarkMode: !state.isDarkMode,
        isSystemTheme: false,
      }
    
    case THEME_ACTIONS.SET_SYSTEM_THEME:
      const systemTheme = getSystemTheme()
      return {
        ...state,
        currentTheme: systemTheme,
        isDarkMode: systemTheme === THEMES.DARK,
        isSystemTheme: true,
      }
    
    default:
      return state
  }
}

// Helper functions
const isDarkTheme = (theme) => {
  const darkThemes = [
    THEMES.DARK,
    THEMES.SYNTHWAVE,
    THEMES.HALLOWEEN,
    THEMES.FOREST,
    THEMES.BLACK,
    THEMES.LUXURY,
    THEMES.DRACULA,
    THEMES.NIGHT,
    THEMES.COFFEE,
  ]
  return darkThemes.includes(theme)
}

const getSystemTheme = () => {
  if (typeof window !== 'undefined' && window.matchMedia) {
    return window.matchMedia('(prefers-color-scheme: dark)').matches 
      ? THEMES.DARK 
      : THEMES.LIGHT
  }
  return THEMES.LIGHT
}

// Create context
const ThemeContext = createContext()

// Custom hook to use theme context
export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

// Theme provider component
export const ThemeProvider = ({ children }) => {
  const [state, dispatch] = useReducer(themeReducer, initialState)

  // Initialize theme on mount
  useEffect(() => {
    const savedTheme = getTheme()
    if (savedTheme && Object.values(THEMES).includes(savedTheme)) {
      setTheme(savedTheme)
    } else {
      // Use system theme if no saved theme
      dispatch({ type: THEME_ACTIONS.SET_SYSTEM_THEME })
    }
  }, [])

  // Listen for system theme changes
  useEffect(() => {
    if (state.isSystemTheme && typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      
      const handleChange = (e) => {
        if (state.isSystemTheme) {
          const newTheme = e.matches ? THEMES.DARK : THEMES.LIGHT
          setTheme(newTheme)
        }
      }

      mediaQuery.addEventListener('change', handleChange)
      return () => mediaQuery.removeEventListener('change', handleChange)
    }
  }, [state.isSystemTheme])

  // Apply theme to document
  useEffect(() => {
    setStoredTheme(state.currentTheme)
  }, [state.currentTheme])

  // Set theme function
  const setTheme = (theme) => {
    if (Object.values(THEMES).includes(theme)) {
      dispatch({
        type: THEME_ACTIONS.SET_THEME,
        payload: theme,
      })
    }
  }

  // Toggle dark mode
  const toggleDarkMode = () => {
    dispatch({ type: THEME_ACTIONS.TOGGLE_DARK_MODE })
  }

  // Use system theme
  const useSystemTheme = () => {
    dispatch({ type: THEME_ACTIONS.SET_SYSTEM_THEME })
  }

  // Get theme info
  const getThemeInfo = (theme = state.currentTheme) => {
    for (const category of Object.values(THEME_CATEGORIES)) {
      if (category.themes.includes(theme)) {
        return {
          name: theme,
          category: category.name,
          isDark: isDarkTheme(theme),
        }
      }
    }
    return {
      name: theme,
      category: 'Unknown',
      isDark: isDarkTheme(theme),
    }
  }

  // Get available themes by category
  const getThemesByCategory = () => {
    return THEME_CATEGORIES
  }

  // Get all available themes
  const getAllThemes = () => {
    return Object.values(THEMES)
  }

  // Check if current theme is dark
  const isCurrentThemeDark = () => {
    return state.isDarkMode
  }

  // Get theme display name
  const getThemeDisplayName = (theme) => {
    return theme.charAt(0).toUpperCase() + theme.slice(1)
  }

  // Context value
  const value = {
    // State
    currentTheme: state.currentTheme,
    isDarkMode: state.isDarkMode,
    isSystemTheme: state.isSystemTheme,
    
    // Actions
    setTheme,
    toggleDarkMode,
    useSystemTheme,
    
    // Utilities
    getThemeInfo,
    getThemesByCategory,
    getAllThemes,
    isCurrentThemeDark,
    getThemeDisplayName,
    
    // Constants
    THEMES,
    THEME_CATEGORIES,
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  )
}
