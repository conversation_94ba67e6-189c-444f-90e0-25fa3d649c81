import React, { useEffect, useState } from 'react'

const PerformanceMonitor = ({ showInDev = true }) => {
  const [metrics, setMetrics] = useState({
    fps: 0,
    memory: 0,
    webglContexts: 0
  })

  useEffect(() => {
    if (!showInDev && process.env.NODE_ENV === 'production') return

    let frameCount = 0
    let lastTime = performance.now()
    let animationId

    const updateMetrics = () => {
      frameCount++
      const currentTime = performance.now()
      
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime))
        
        // Get memory usage if available
        const memory = performance.memory ? 
          Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) : 0

        // Count WebGL contexts (approximate)
        const canvas = document.querySelectorAll('canvas')
        const webglContexts = Array.from(canvas).filter(c => {
          try {
            return c.getContext('webgl') || c.getContext('experimental-webgl')
          } catch (e) {
            return false
          }
        }).length

        setMetrics({ fps, memory, webglContexts })
        
        frameCount = 0
        lastTime = currentTime
      }
      
      animationId = requestAnimationFrame(updateMetrics)
    }

    animationId = requestAnimationFrame(updateMetrics)

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId)
      }
    }
  }, [showInDev])

  if (!showInDev && process.env.NODE_ENV === 'production') return null

  return (
    <div className="fixed top-4 right-4 z-50 bg-base-100/90 backdrop-blur-sm rounded-lg p-3 text-xs font-mono border border-base-300 shadow-lg">
      <div className="space-y-1">
        <div className="flex justify-between gap-4">
          <span>FPS:</span>
          <span className={metrics.fps < 30 ? 'text-error' : metrics.fps < 50 ? 'text-warning' : 'text-success'}>
            {metrics.fps}
          </span>
        </div>
        {metrics.memory > 0 && (
          <div className="flex justify-between gap-4">
            <span>Memory:</span>
            <span className={metrics.memory > 100 ? 'text-warning' : 'text-base-content'}>
              {metrics.memory}MB
            </span>
          </div>
        )}
        <div className="flex justify-between gap-4">
          <span>WebGL:</span>
          <span className={metrics.webglContexts > 2 ? 'text-error' : 'text-base-content'}>
            {metrics.webglContexts}
          </span>
        </div>
      </div>
    </div>
  )
}

export default PerformanceMonitor
