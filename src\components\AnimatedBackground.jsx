import React from 'react'

const AnimatedBackground = ({
  type = 'particles',
  className = '',
  children,
  options = {}
}) => {
  // Get simple background class based on type using DaisyUI colors
  const getBackgroundClass = () => {
    switch (type) {
      case 'waves':
        return 'bg-gradient-to-br from-primary/20 via-secondary/10 to-accent/20'
      case 'net':
        return 'bg-gradient-to-br from-secondary/20 via-accent/10 to-primary/20'
      case 'clouds':
        return 'bg-gradient-to-br from-info/20 via-primary/10 to-secondary/20'
      case 'particles':
      default:
        return 'bg-gradient-to-br from-base-200 via-base-100 to-base-200'
    }
  }

  return (
    <div className={`relative overflow-hidden ${getBackgroundClass()} ${className}`}>
      {children && (
        <div className="relative z-10">
          {children}
        </div>
      )}
    </div>
  )
}

// Preset configurations for different background types
export const BackgroundPresets = {
  hero: {
    type: 'waves',
    className: 'min-h-screen',
    options: {}
  },
  dashboard: {
    type: 'particles',
    className: 'min-h-screen',
    options: {}
  },
  course: {
    type: 'net',
    className: 'min-h-96',
    options: {}
  },
  quiz: {
    type: 'clouds',
    className: 'min-h-screen',
    options: {}
  }
}

// Higher-order component for easy background application
export const withAnimatedBackground = (Component, backgroundType = 'particles') => {
  return function WrappedComponent(props) {
    const preset = BackgroundPresets[backgroundType] || BackgroundPresets.dashboard

    return (
      <AnimatedBackground {...preset}>
        <Component {...props} />
      </AnimatedBackground>
    )
  }
}

export default AnimatedBackground
