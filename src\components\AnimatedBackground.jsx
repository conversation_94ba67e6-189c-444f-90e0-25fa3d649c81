import React, { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'

const AnimatedBackground = ({
  type = 'particles',
  className = '',
  children,
  options = {}
}) => {
  const containerRef = useRef(null)

  useEffect(() => {
    if (!containerRef.current) return

    // Create animation based on type
    switch (type) {
      case 'waves':
        createWavesAnimation()
        break
      case 'net':
        createNetAnimation()
        break
      case 'clouds':
        createCloudsAnimation()
        break
      case 'particles':
      default:
        createParticleSystem()
        break
    }

    // Cleanup function
    return () => {
      if (containerRef.current) {
        gsap.killTweensOf(containerRef.current.querySelectorAll('*'))
      }
    }
  }, [type])

  const createParticleSystem = () => {
    const container = containerRef.current
    if (!container) return

    // Clear any existing content
    container.innerHTML = ''

    // Create background gradient
    const background = document.createElement('div')
    background.className = 'absolute inset-0 bg-gradient-to-br from-primary/10 via-secondary/5 to-accent/10'
    container.appendChild(background)

    // Create particles
    const particleCount = 30
    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div')
      particle.className = 'absolute w-1 h-1 bg-primary/40 rounded-full'
      particle.style.left = Math.random() * 100 + '%'
      particle.style.top = Math.random() * 100 + '%'
      container.appendChild(particle)

      // Animate particles with GSAP
      gsap.to(particle, {
        x: (Math.random() - 0.5) * 100,
        y: (Math.random() - 0.5) * 100,
        opacity: Math.random() * 0.8 + 0.2,
        duration: Math.random() * 4 + 3,
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut",
        delay: Math.random() * 2
      })
    }
  }

  const createWavesAnimation = () => {
    const container = containerRef.current
    if (!container) return

    container.innerHTML = ''

    // Create wave layers
    for (let i = 0; i < 3; i++) {
      const wave = document.createElement('div')
      wave.className = `absolute inset-0 opacity-${30 - i * 10}`
      wave.innerHTML = `
        <div class="absolute inset-0 bg-gradient-to-br from-blue-500/20 via-cyan-500/10 to-blue-600/20"></div>
        <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(59,130,246,0.15),transparent_70%)]"></div>
      `
      container.appendChild(wave)

      // Animate waves
      gsap.to(wave, {
        backgroundPosition: "200% 200%",
        duration: 8 + i * 2,
        repeat: -1,
        ease: "none"
      })
    }
  }

  const createNetAnimation = () => {
    const container = containerRef.current
    if (!container) return

    container.innerHTML = ''

    // Create network background
    const background = document.createElement('div')
    background.className = 'absolute inset-0 bg-gradient-to-br from-purple-500/10 via-indigo-500/5 to-purple-600/10'
    container.appendChild(background)

    // Create network nodes
    const nodeCount = 15

    for (let i = 0; i < nodeCount; i++) {
      const node = document.createElement('div')
      node.className = 'absolute w-2 h-2 bg-purple-500/50 rounded-full'
      node.style.left = Math.random() * 100 + '%'
      node.style.top = Math.random() * 100 + '%'
      container.appendChild(node)

      // Animate nodes
      gsap.to(node, {
        x: (Math.random() - 0.5) * 50,
        y: (Math.random() - 0.5) * 50,
        scale: Math.random() * 0.5 + 0.5,
        duration: Math.random() * 3 + 2,
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut",
        delay: Math.random() * 2
      })
    }
  }

  const createCloudsAnimation = () => {
    const container = containerRef.current
    if (!container) return

    container.innerHTML = ''

    // Add sky gradient
    const sky = document.createElement('div')
    sky.className = 'absolute inset-0 bg-gradient-to-br from-sky-400/10 via-blue-300/5 to-indigo-400/10'
    container.appendChild(sky)

    // Create cloud layers
    for (let i = 0; i < 4; i++) {
      const cloud = document.createElement('div')
      cloud.className = `absolute w-32 h-16 bg-white/10 rounded-full opacity-${20 - i * 3}`
      cloud.style.left = Math.random() * 100 + '%'
      cloud.style.top = Math.random() * 100 + '%'
      cloud.style.filter = 'blur(1px)'
      container.appendChild(cloud)

      // Animate clouds
      gsap.to(cloud, {
        x: 100 + Math.random() * 50,
        duration: 15 + Math.random() * 10,
        repeat: -1,
        ease: "none",
        delay: Math.random() * 5
      })
    }
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        duration: 1,
        ease: "easeOut"
      }
    }
  }

  return (
    <motion.div
      ref={containerRef}
      className={`relative overflow-hidden ${className}`}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {children && (
        <div className="relative z-10">
          {children}
        </div>
      )}
    </motion.div>
  )
}

// Preset configurations for different background types
export const BackgroundPresets = {
  hero: {
    type: 'waves',
    className: 'min-h-screen',
    options: {}
  },
  dashboard: {
    type: 'particles',
    className: 'min-h-screen',
    options: {}
  },
  course: {
    type: 'net',
    className: 'min-h-96',
    options: {}
  },
  quiz: {
    type: 'clouds',
    className: 'min-h-screen',
    options: {}
  }
}

// Higher-order component for easy background application
export const withAnimatedBackground = (Component, backgroundType = 'particles') => {
  return function WrappedComponent(props) {
    const preset = BackgroundPresets[backgroundType] || BackgroundPresets.dashboard

    return (
      <AnimatedBackground {...preset}>
        <Component {...props} />
      </AnimatedBackground>
    )
  }
}

export default AnimatedBackground
