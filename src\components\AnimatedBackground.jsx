import React, { useEffect, useRef, useState } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import * as THREE from 'three'

const AnimatedBackground = ({ 
  type = 'particles', 
  className = '', 
  children,
  options = {} 
}) => {
  const vantaRef = useRef(null)
  const [vantaEffect, setVantaEffect] = useState(null)

  useEffect(() => {
    if (!vantaRef.current) return

    let effect = null

    const loadVantaEffect = async () => {
      try {
        // Dynamically import Vanta effects
        let VANTA

        switch (type) {
          case 'waves':
            try {
              VANTA = await import('vanta/dist/vanta.waves.min.js')
              effect = VANTA.default({
                el: vantaRef.current,
                THREE: THREE,
                mouseControls: true,
                touchControls: true,
                gyroControls: false,
                minHeight: 200.00,
                minWidth: 200.00,
                scale: 1.00,
                scaleMobile: 1.00,
                color: 0x3b82f6,
                shininess: 30.00,
                waveHeight: 15.00,
                waveSpeed: 0.75,
                zoom: 0.65,
                ...options
              })
            } catch (importError) {
              console.warn('Failed to load Vanta WAVES effect:', importError)
              createCSSFallback()
              return
            }
            break

          case 'net':
            try {
              VANTA = await import('vanta/dist/vanta.net.min.js')
              effect = VANTA.default({
                el: vantaRef.current,
                THREE: THREE,
                mouseControls: true,
                touchControls: true,
                gyroControls: false,
                minHeight: 200.00,
                minWidth: 200.00,
                scale: 1.00,
                scaleMobile: 1.00,
                color: 0x3b82f6,
                backgroundColor: 0x111827,
                points: 10.00,
                maxDistance: 20.00,
                spacing: 15.00,
                ...options
              })
            } catch (importError) {
              console.warn('Failed to load Vanta NET effect:', importError)
              createCSSFallback()
              return
            }
            break

          case 'clouds':
            try {
              VANTA = await import('vanta/dist/vanta.clouds.min.js')
              effect = VANTA.default({
                el: vantaRef.current,
                THREE: THREE,
                mouseControls: true,
                touchControls: true,
                gyroControls: false,
                minHeight: 200.00,
                minWidth: 200.00,
                skyColor: 0x68b8d7,
                cloudColor: 0xadc1de,
                cloudShadowColor: 0x183550,
                sunColor: 0xff9919,
                sunGlareColor: 0xff6633,
                sunlightColor: 0xff9933,
                speed: 1.00,
                ...options
              })
            } catch (importError) {
              console.warn('Failed to load Vanta CLOUDS effect:', importError)
              createCSSFallback()
              return
            }
            break

          case 'particles':
          default:
            // Custom particle system using GSAP
            createParticleSystem()
            return
        }

        if (effect) {
          setVantaEffect(effect)
        }
      } catch (error) {
        console.warn('Vanta effect failed to load:', error)
        // Fallback to CSS animation
        createCSSFallback()
      }
    }

    loadVantaEffect()

    return () => {
      if (effect) {
        try {
          effect.destroy()
        } catch (destroyError) {
          console.warn('Error destroying Vanta effect:', destroyError)
        }
      }
    }
  }, [type, options])

  const createParticleSystem = () => {
    const container = vantaRef.current
    if (!container) return

    // Clear any existing particles
    container.innerHTML = ''

    // Create particles
    const particleCount = 50
    const particles = []

    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div')
      particle.className = 'absolute w-1 h-1 bg-primary/30 rounded-full'
      particle.style.left = Math.random() * 100 + '%'
      particle.style.top = Math.random() * 100 + '%'
      container.appendChild(particle)
      particles.push(particle)

      // Animate particles with GSAP
      gsap.to(particle, {
        x: (Math.random() - 0.5) * 200,
        y: (Math.random() - 0.5) * 200,
        opacity: Math.random() * 0.8 + 0.2,
        scale: Math.random() * 2 + 0.5,
        duration: Math.random() * 10 + 5,
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut"
      })
    }

    // Add floating animation
    gsap.to(particles, {
      y: "+=20",
      duration: 3,
      repeat: -1,
      yoyo: true,
      ease: "power2.inOut",
      stagger: {
        amount: 2,
        from: "random"
      }
    })
  }

  const createCSSFallback = () => {
    const container = vantaRef.current
    if (!container) return

    // Clear any existing content
    container.innerHTML = ''

    // Create animated CSS background based on type
    const fallbackDiv = document.createElement('div')
    fallbackDiv.className = 'absolute inset-0 overflow-hidden'

    switch (type) {
      case 'waves':
        fallbackDiv.innerHTML = `
          <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-cyan-500/5 to-blue-600/10"></div>
          <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(59,130,246,0.1),transparent_70%)]"></div>
          <div class="absolute inset-0 animate-pulse bg-gradient-to-r from-transparent via-blue-500/5 to-transparent"></div>
        `
        break
      case 'net':
        fallbackDiv.innerHTML = `
          <div class="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-indigo-500/5 to-purple-600/10"></div>
          <div class="absolute inset-0 bg-[conic-gradient(from_0deg,transparent,rgba(139,92,246,0.1),transparent)]"></div>
        `
        break
      case 'clouds':
        fallbackDiv.innerHTML = `
          <div class="absolute inset-0 bg-gradient-to-br from-sky-400/10 via-blue-300/5 to-indigo-400/10"></div>
          <div class="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(56,189,248,0.1),transparent_50%)]"></div>
        `
        break
      default:
        fallbackDiv.innerHTML = `
          <div class="absolute inset-0 bg-gradient-to-br from-primary/10 via-secondary/5 to-accent/10"></div>
          <div class="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.1),transparent_50%)]"></div>
        `
    }

    container.appendChild(fallbackDiv)

    // Add some animated elements for the particles fallback
    if (type === 'particles') {
      createParticleSystem()
    }
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        duration: 1,
        ease: "easeOut"
      }
    }
  }

  return (
    <motion.div
      ref={vantaRef}
      className={`relative overflow-hidden ${className}`}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {children && (
        <div className="relative z-10">
          {children}
        </div>
      )}
    </motion.div>
  )
}

// Preset configurations for different background types
export const BackgroundPresets = {
  hero: {
    type: 'waves',
    className: 'min-h-screen',
    options: {
      color: 0x3b82f6,
      waveHeight: 20,
      waveSpeed: 1.2,
      zoom: 0.8
    }
  },
  dashboard: {
    type: 'particles',
    className: 'min-h-screen',
    options: {}
  },
  course: {
    type: 'net',
    className: 'min-h-96',
    options: {
      color: 0x8b5cf6,
      points: 8,
      maxDistance: 25
    }
  },
  quiz: {
    type: 'clouds',
    className: 'min-h-screen',
    options: {
      skyColor: 0x87ceeb,
      speed: 0.8
    }
  }
}

// Higher-order component for easy background application
export const withAnimatedBackground = (Component, backgroundType = 'particles') => {
  return function WrappedComponent(props) {
    const preset = BackgroundPresets[backgroundType] || BackgroundPresets.dashboard

    return (
      <AnimatedBackground {...preset}>
        <Component {...props} />
      </AnimatedBackground>
    )
  }
}

export default AnimatedBackground
