import React, { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'

const AnimatedBackground = ({
  type = 'particles',
  className = '',
  children,
  options = {}
}) => {
  const containerRef = useRef(null)

  useEffect(() => {
    // Optional: Add any additional GSAP animations here if needed
    // For now, we're using pure CSS animations which are more performant

    return () => {
      // Cleanup any GSAP animations if we add them later
      if (containerRef.current) {
        gsap.killTweensOf(containerRef.current.querySelectorAll('*'))
      }
    }
  }, [type])

  // All animations are now pure CSS for optimal performance and compatibility

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        duration: 1,
        ease: "easeOut"
      }
    }
  }

  // Get animated background class based on type
  const getAnimatedClass = () => {
    switch (type) {
      case 'waves':
        return 'animated-bg-waves'
      case 'net':
        return 'animated-bg-net'
      case 'clouds':
        return 'animated-bg-clouds'
      case 'particles':
      default:
        return 'animated-bg-particles'
    }
  }

  return (
    <motion.div
      ref={containerRef}
      className={`relative overflow-hidden ${getAnimatedClass()} ${className}`}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {children && (
        <div className="relative z-10">
          {children}
        </div>
      )}
    </motion.div>
  )
}

// Preset configurations for different background types
export const BackgroundPresets = {
  hero: {
    type: 'waves',
    className: 'min-h-screen',
    options: {}
  },
  dashboard: {
    type: 'particles',
    className: 'min-h-screen',
    options: {}
  },
  course: {
    type: 'net',
    className: 'min-h-96',
    options: {}
  },
  quiz: {
    type: 'clouds',
    className: 'min-h-screen',
    options: {}
  }
}

// Higher-order component for easy background application
export const withAnimatedBackground = (Component, backgroundType = 'particles') => {
  return function WrappedComponent(props) {
    const preset = BackgroundPresets[backgroundType] || BackgroundPresets.dashboard

    return (
      <AnimatedBackground {...preset}>
        <Component {...props} />
      </AnimatedBackground>
    )
  }
}

export default AnimatedBackground
