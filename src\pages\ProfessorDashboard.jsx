import React, { useState, useEffect } from 'react'
import { Routes, Route } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import Navbar from '../components/Navbar'
import Sidebar from '../components/Sidebar'
import ScoreChart from '../components/ScoreChart'
import QuizCard, { QuizGrid } from '../components/QuizCard'
import AnimatedBackground from '../components/AnimatedBackground'

const ProfessorDashboard = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [courses, setCourses] = useState([])
  const [quizzes, setQuizzes] = useState([])
  const [loading, setLoading] = useState(true)
  const [showCreateQuiz, setShowCreateQuiz] = useState(false)
  const [showUploadContent, setShowUploadContent] = useState(false)

  const { user } = useAuth()

  useEffect(() => {
    // Simulate loading data
    setTimeout(() => {
      setCourses([
        { id: 1, title: 'Introduction to Computer Science', students: 45, progress: 78, status: 'active' },
        { id: 2, title: 'Data Structures & Algorithms', students: 32, progress: 65, status: 'active' },
        { id: 3, title: 'Web Development Fundamentals', students: 28, progress: 82, status: 'active' }
      ])
      
      setQuizzes([
        { 
          id: 1, 
          title: 'JavaScript Fundamentals', 
          description: 'Test your knowledge of JavaScript basics',
          subject: 'Programming',
          difficulty: 'easy',
          questionCount: 15,
          duration: 30,
          createdAt: '2024-01-10',
          attempts: 23,
          averageScore: 78
        },
        { 
          id: 2, 
          title: 'Data Structures Quiz', 
          description: 'Arrays, linked lists, and basic algorithms',
          subject: 'Computer Science',
          difficulty: 'medium',
          questionCount: 20,
          duration: 45,
          createdAt: '2024-01-08',
          attempts: 18,
          averageScore: 65
        },
        { 
          id: 3, 
          title: 'Advanced React Concepts', 
          description: 'Hooks, context, and performance optimization',
          subject: 'Web Development',
          difficulty: 'hard',
          questionCount: 25,
          duration: 60,
          createdAt: '2024-01-05',
          attempts: 12,
          averageScore: 72
        }
      ])
      setLoading(false)
    }, 1000)
  }, [])

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    }
  }

  // Mock data for analytics
  const studentPerformanceData = [
    { name: 'Week 1', average: 65, highest: 95, lowest: 45 },
    { name: 'Week 2', average: 72, highest: 98, lowest: 52 },
    { name: 'Week 3', average: 78, highest: 96, lowest: 58 },
    { name: 'Week 4', average: 75, highest: 94, lowest: 55 },
    { name: 'Week 5', average: 82, highest: 99, lowest: 62 },
    { name: 'Week 6', average: 85, highest: 97, lowest: 68 }
  ]

  const subjectDistribution = [
    { name: 'Programming', value: 45, color: '#3b82f6' },
    { name: 'Theory', value: 30, color: '#8b5cf6' },
    { name: 'Projects', value: 25, color: '#06b6d4' }
  ]

  const handleCreateQuiz = () => {
    setShowCreateQuiz(true)
  }

  const handleUploadContent = () => {
    setShowUploadContent(true)
  }

  const handleEditQuiz = (quiz) => {
    console.log('Edit quiz:', quiz)
  }

  const handleViewResults = (quiz) => {
    console.log('View results:', quiz)
  }

  const handlePredictScore = async () => {
    // Mock AI score prediction
    const prediction = Math.floor(Math.random() * 20) + 70 // 70-90%
    alert(`AI Prediction: Students are likely to score ${prediction}% on average based on current performance trends.`)
  }

  const MainDashboard = () => (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Welcome Section */}
      <motion.div variants={itemVariants}>
        <div className="hero bg-gradient-to-r from-secondary to-accent text-secondary-content rounded-2xl">
          <div className="hero-content text-center py-12">
            <div className="max-w-md">
              <motion.h1 
                className="text-4xl font-bold mb-4"
                animate={{ 
                  backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
                }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                Welcome, Prof. {user?.lastName || 'Professor'}! 👨‍🏫
              </motion.h1>
              <p className="text-lg opacity-90">
                Manage your courses, create engaging quizzes, and track student progress
              </p>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Quick Actions */}
      <motion.div 
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        variants={containerVariants}
      >
        {[
          { 
            title: 'Create Quiz', 
            description: 'Design new assessments',
            icon: '📝', 
            color: 'from-blue-500 to-blue-600',
            action: handleCreateQuiz
          },
          { 
            title: 'Upload Content', 
            description: 'Add course materials',
            icon: '📤', 
            color: 'from-green-500 to-green-600',
            action: handleUploadContent
          },
          { 
            title: 'Predict Scores', 
            description: 'AI-powered analytics',
            icon: '🔮', 
            color: 'from-purple-500 to-purple-600',
            action: handlePredictScore
          },
          { 
            title: 'View Analytics', 
            description: 'Student performance',
            icon: '📊', 
            color: 'from-orange-500 to-orange-600',
            action: () => console.log('View analytics')
          }
        ].map((action, index) => (
          <motion.div
            key={index}
            variants={itemVariants}
            whileHover={{ scale: 1.05, y: -5 }}
            className="card bg-base-100 shadow-lg border border-base-300 overflow-hidden cursor-pointer"
            onClick={action.action}
          >
            <div className={`h-2 bg-gradient-to-r ${action.color}`}></div>
            <div className="card-body p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-bold text-lg">{action.title}</h3>
                  <p className="text-base-content/60 text-sm">{action.description}</p>
                </div>
                <div className="text-3xl">{action.icon}</div>
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* My Courses */}
      <motion.div variants={itemVariants}>
        <div className="card bg-base-100 shadow-lg border border-base-300">
          <div className="card-body">
            <div className="flex items-center justify-between mb-4">
              <h3 className="card-title">My Courses</h3>
              <motion.button 
                className="btn btn-primary btn-sm"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Create New Course
              </motion.button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {courses.map((course, index) => (
                <motion.div
                  key={course.id}
                  className="card bg-base-200 shadow-md"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="card-body p-4">
                    <h4 className="font-semibold">{course.title}</h4>
                    <div className="flex items-center justify-between text-sm text-base-content/60 mt-2">
                      <span>{course.students} students</span>
                      <span className={`badge ${course.status === 'active' ? 'badge-success' : 'badge-warning'}`}>
                        {course.status}
                      </span>
                    </div>
                    <div className="mt-3">
                      <div className="flex items-center justify-between text-xs mb-1">
                        <span>Progress</span>
                        <span>{course.progress}%</span>
                      </div>
                      <progress className="progress progress-primary w-full" value={course.progress} max="100"></progress>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Analytics Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div variants={itemVariants}>
          <ScoreChart
            data={studentPerformanceData}
            type="line"
            title="Student Performance Trends"
            height={350}
          />
        </motion.div>
        <motion.div variants={itemVariants}>
          <ScoreChart
            data={subjectDistribution}
            type="pie"
            title="Assessment Distribution"
            height={350}
          />
        </motion.div>
      </div>

      {/* Recent Quizzes */}
      <motion.div variants={itemVariants}>
        <div className="card bg-base-100 shadow-lg border border-base-300">
          <div className="card-body">
            <div className="flex items-center justify-between mb-4">
              <h3 className="card-title">Recent Quizzes</h3>
              <motion.button 
                className="btn btn-primary btn-sm"
                onClick={handleCreateQuiz}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Create Quiz
              </motion.button>
            </div>
            <QuizGrid
              quizzes={quizzes}
              onEdit={handleEditQuiz}
              onViewResults={handleViewResults}
              loading={loading}
              emptyMessage="No quizzes created yet. Create your first quiz!"
            />
          </div>
        </div>
      </motion.div>
    </motion.div>
  )

  return (
    <div className="min-h-screen bg-base-100">
      <Navbar onSidebarToggle={() => setSidebarOpen(!sidebarOpen)} />
      
      <div className="flex">
        <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />
        
        <main className="flex-1 p-6 lg:ml-0">
          <AnimatedBackground type="particles" className="fixed inset-0 -z-10" />
          
          <Routes>
            <Route path="/" element={<MainDashboard />} />
            <Route path="/courses" element={<div>My Courses (Coming Soon)</div>} />
            <Route path="/quiz/create" element={<div>Create Quiz (Coming Soon)</div>} />
            <Route path="/analytics" element={<div>Student Analytics (Coming Soon)</div>} />
            <Route path="/upload" element={<div>Upload Content (Coming Soon)</div>} />
          </Routes>
        </main>
      </div>

      {/* Create Quiz Modal */}
      <AnimatePresence>
        {showCreateQuiz && (
          <motion.div
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowCreateQuiz(false)}
          >
            <motion.div
              className="bg-base-100 rounded-2xl p-6 max-w-md w-full"
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.5, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-2xl font-bold mb-4">Create New Quiz</h3>
              <p className="text-base-content/70 mb-6">
                This feature will allow you to create interactive quizzes with AI assistance.
              </p>
              <div className="flex justify-end space-x-2">
                <button 
                  className="btn btn-outline"
                  onClick={() => setShowCreateQuiz(false)}
                >
                  Cancel
                </button>
                <button className="btn btn-primary">
                  Coming Soon
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Upload Content Modal */}
      <AnimatePresence>
        {showUploadContent && (
          <motion.div
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowUploadContent(false)}
          >
            <motion.div
              className="bg-base-100 rounded-2xl p-6 max-w-md w-full"
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.5, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-2xl font-bold mb-4">Upload Content</h3>
              <p className="text-base-content/70 mb-6">
                Upload course materials, videos, documents, and other learning resources.
              </p>
              <div className="flex justify-end space-x-2">
                <button 
                  className="btn btn-outline"
                  onClick={() => setShowUploadContent(false)}
                >
                  Cancel
                </button>
                <button className="btn btn-primary">
                  Coming Soon
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default ProfessorDashboard
