import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuth } from './contexts/AuthContext'

// Import pages (will be created in next tasks)
import Login from './pages/Login'
import Register from './pages/Register'
import AdminDashboard from './pages/AdminDashboard'
import Professor<PERSON>ashboard from './pages/ProfessorDashboard'
import StudentDashboard from './pages/StudentDashboard'
import NotFound from './pages/NotFound'

// Route Guard Component
const RouteGuard = ({ children, requiredRole = null, requireAuth = true }) => {
  const { isAuthenticated, user, isLoading } = useAuth()

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="loading loading-spinner loading-lg text-primary"></div>
      </div>
    )
  }

  // Redirect to login if authentication is required but user is not authenticated
  if (requireAuth && !isAuthenticated) {
    return <Navigate to="/login" replace />
  }

  // Redirect to appropriate dashboard if user is authenticated but trying to access auth pages
  if (!requireAuth && isAuthenticated) {
    return <Navigate to={getDashboardRoute(user?.role)} replace />
  }

  // Check role-based access
  if (requiredRole && user?.role !== requiredRole) {
    return <Navigate to={getDashboardRoute(user?.role)} replace />
  }

  return children
}

// Helper function to get dashboard route based on role
const getDashboardRoute = (role) => {
  switch (role) {
    case 'admin':
      return '/admin'
    case 'professor':
      return '/professor'
    case 'student':
      return '/student'
    default:
      return '/login'
  }
}

// Public Route Component (for login/register pages)
const PublicRoute = ({ children }) => {
  return (
    <RouteGuard requireAuth={false}>
      {children}
    </RouteGuard>
  )
}

// Protected Route Component
const ProtectedRoute = ({ children, requiredRole = null }) => {
  return (
    <RouteGuard requiredRole={requiredRole}>
      {children}
    </RouteGuard>
  )
}

// Admin Route Component
const AdminRoute = ({ children }) => {
  return (
    <ProtectedRoute requiredRole="admin">
      {children}
    </ProtectedRoute>
  )
}

// Professor Route Component
const ProfessorRoute = ({ children }) => {
  return (
    <ProtectedRoute requiredRole="professor">
      {children}
    </ProtectedRoute>
  )
}

// Student Route Component
const StudentRoute = ({ children }) => {
  return (
    <ProtectedRoute requiredRole="student">
      {children}
    </ProtectedRoute>
  )
}

// Main App Router
const AppRouter = () => {
  const { isAuthenticated, user } = useAuth()

  return (
    <Routes>
      {/* Public Routes */}
      <Route 
        path="/login" 
        element={
          <PublicRoute>
            <Login />
          </PublicRoute>
        } 
      />
      <Route 
        path="/register" 
        element={
          <PublicRoute>
            <Register />
          </PublicRoute>
        } 
      />

      {/* Protected Routes - Admin */}
      <Route 
        path="/admin/*" 
        element={
          <AdminRoute>
            <AdminDashboard />
          </AdminRoute>
        } 
      />

      {/* Protected Routes - Professor */}
      <Route 
        path="/professor/*" 
        element={
          <ProfessorRoute>
            <ProfessorDashboard />
          </ProfessorRoute>
        } 
      />

      {/* Protected Routes - Student */}
      <Route 
        path="/student/*" 
        element={
          <StudentRoute>
            <StudentDashboard />
          </StudentRoute>
        } 
      />

      {/* Root Route - Redirect based on authentication and role */}
      <Route 
        path="/" 
        element={
          isAuthenticated ? (
            <Navigate to={getDashboardRoute(user?.role)} replace />
          ) : (
            <Navigate to="/login" replace />
          )
        } 
      />

      {/* Catch all route - 404 */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  )
}

export default AppRouter

// Export route components for reuse
export {
  RouteGuard,
  PublicRoute,
  ProtectedRoute,
  AdminRoute,
  ProfessorRoute,
  StudentRoute,
}
