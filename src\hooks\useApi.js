import { useState, useEffect, useCallback } from 'react'

// Custom hook for API calls with loading, error, and data states
export const useApi = (apiFunction, dependencies = [], options = {}) => {
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  
  const {
    immediate = true,
    onSuccess,
    onError,
    transform,
  } = options

  const execute = useCallback(async (...args) => {
    try {
      setLoading(true)
      setError(null)
      
      const result = await apiFunction(...args)
      const transformedData = transform ? transform(result) : result
      
      setData(transformedData)
      
      if (onSuccess) {
        onSuccess(transformedData)
      }
      
      return { success: true, data: transformedData }
    } catch (err) {
      const errorMessage = err.message || 'An error occurred'
      setError(errorMessage)
      
      if (onError) {
        onError(err)
      }
      
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [apiFunction, onSuccess, onError, transform])

  useEffect(() => {
    if (immediate) {
      execute()
    }
  }, dependencies)

  const reset = useCallback(() => {
    setData(null)
    setError(null)
    setLoading(false)
  }, [])

  return {
    data,
    loading,
    error,
    execute,
    reset,
  }
}

// Hook for paginated API calls
export const usePaginatedApi = (apiFunction, options = {}) => {
  const [data, setData] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [hasMore, setHasMore] = useState(true)
  const [page, setPage] = useState(1)
  
  const {
    pageSize = 10,
    onSuccess,
    onError,
  } = options

  const loadMore = useCallback(async () => {
    if (loading || !hasMore) return

    try {
      setLoading(true)
      setError(null)
      
      const result = await apiFunction({ page, pageSize })
      const newData = Array.isArray(result) ? result : result.data || []
      
      if (page === 1) {
        setData(newData)
      } else {
        setData(prev => [...prev, ...newData])
      }
      
      setHasMore(newData.length === pageSize)
      setPage(prev => prev + 1)
      
      if (onSuccess) {
        onSuccess(newData)
      }
    } catch (err) {
      const errorMessage = err.message || 'An error occurred'
      setError(errorMessage)
      
      if (onError) {
        onError(err)
      }
    } finally {
      setLoading(false)
    }
  }, [apiFunction, page, pageSize, loading, hasMore, onSuccess, onError])

  const reset = useCallback(() => {
    setData([])
    setError(null)
    setLoading(false)
    setHasMore(true)
    setPage(1)
  }, [])

  const refresh = useCallback(() => {
    setPage(1)
    setHasMore(true)
    loadMore()
  }, [loadMore])

  return {
    data,
    loading,
    error,
    hasMore,
    loadMore,
    reset,
    refresh,
  }
}

// Hook for form submissions
export const useFormSubmit = (submitFunction, options = {}) => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [success, setSuccess] = useState(false)
  
  const {
    onSuccess,
    onError,
    resetOnSuccess = true,
  } = options

  const submit = useCallback(async (formData) => {
    try {
      setLoading(true)
      setError(null)
      setSuccess(false)
      
      const result = await submitFunction(formData)
      
      setSuccess(true)
      
      if (onSuccess) {
        onSuccess(result)
      }
      
      if (resetOnSuccess) {
        setTimeout(() => setSuccess(false), 3000)
      }
      
      return { success: true, data: result }
    } catch (err) {
      const errorMessage = err.message || 'Submission failed'
      setError(errorMessage)
      
      if (onError) {
        onError(err)
      }
      
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [submitFunction, onSuccess, onError, resetOnSuccess])

  const reset = useCallback(() => {
    setError(null)
    setSuccess(false)
    setLoading(false)
  }, [])

  return {
    loading,
    error,
    success,
    submit,
    reset,
  }
}

// Hook for debounced API calls (useful for search)
export const useDebouncedApi = (apiFunction, delay = 300, options = {}) => {
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [debouncedValue, setDebouncedValue] = useState('')
  
  const {
    onSuccess,
    onError,
    minLength = 1,
  } = options

  const search = useCallback((value) => {
    setDebouncedValue(value)
  }, [])

  useEffect(() => {
    const timer = setTimeout(async () => {
      if (debouncedValue.length >= minLength) {
        try {
          setLoading(true)
          setError(null)
          
          const result = await apiFunction(debouncedValue)
          setData(result)
          
          if (onSuccess) {
            onSuccess(result)
          }
        } catch (err) {
          const errorMessage = err.message || 'Search failed'
          setError(errorMessage)
          
          if (onError) {
            onError(err)
          }
        } finally {
          setLoading(false)
        }
      } else {
        setData(null)
        setError(null)
        setLoading(false)
      }
    }, delay)

    return () => clearTimeout(timer)
  }, [debouncedValue, apiFunction, delay, minLength, onSuccess, onError])

  const reset = useCallback(() => {
    setData(null)
    setError(null)
    setLoading(false)
    setDebouncedValue('')
  }, [])

  return {
    data,
    loading,
    error,
    search,
    reset,
    query: debouncedValue,
  }
}
