import React, { useState, useEffect } from 'react'

// Flash<PERSON>Viewer component for interactive learning

const FlashcardViewer = ({ 
  flashcards = [], 
  onComplete,
  autoPlay = false,
  autoPlayInterval = 5000,
  showProgress = true,
  className = ''
}) => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isFlipped, setIsFlipped] = useState(false)
  const [isAutoPlaying, setIsAutoPlaying] = useState(autoPlay)
  const [studiedCards, setStudiedCards] = useState(new Set())

  const currentCard = flashcards[currentIndex]

  useEffect(() => {
    let interval
    if (isAutoPlaying && flashcards.length > 0) {
      interval = setInterval(() => {
        nextCard()
      }, autoPlayInterval)
    }
    return () => clearInterval(interval)
  }, [isAutoPlaying, currentIndex, autoPlayInterval])

  const flipCard = () => {
    setIsFlipped(!isFlipped)
    if (!isFlipped) {
      setStudiedCards(prev => new Set([...prev, currentIndex]))
    }
  }

  const nextCard = () => {
    if (currentIndex < flashcards.length - 1) {
      setCurrentIndex(currentIndex + 1)
      setIsFlipped(false)
    } else {
      onComplete?.()
    }
  }

  const prevCard = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
      setIsFlipped(false)
    }
  }

  const goToCard = (index) => {
    setCurrentIndex(index)
    setIsFlipped(false)
  }

  const toggleAutoPlay = () => {
    setIsAutoPlaying(!isAutoPlaying)
  }

  const resetSession = () => {
    setCurrentIndex(0)
    setIsFlipped(false)
    setStudiedCards(new Set())
    setIsAutoPlaying(false)
  }

  const cardVariants = {
    enter: (direction) => ({
      x: direction > 0 ? 300 : -300,
      opacity: 0,
      rotateY: 90
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1,
      rotateY: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    exit: (direction) => ({
      zIndex: 0,
      x: direction < 0 ? 300 : -300,
      opacity: 0,
      rotateY: -90,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    })
  }

  const flipVariants = {
    front: {
      rotateY: 0,
      transition: { duration: 0.6 }
    },
    back: {
      rotateY: 180,
      transition: { duration: 0.6 }
    }
  }

  if (!flashcards.length) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">📚</div>
        <h3 className="text-xl font-semibold mb-2">No Flashcards Available</h3>
        <p className="text-base-content/60">Add some flashcards to start studying!</p>
      </div>
    )
  }

  return (
    <div className={`max-w-2xl mx-auto ${className}`}>
      {/* Progress Bar */}
      {showProgress && (
        <motion.div
          className="mb-6"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">
              Card {currentIndex + 1} of {flashcards.length}
            </span>
            <span className="text-sm text-base-content/60">
              {studiedCards.size} studied
            </span>
          </div>
          <div className="w-full bg-base-300 rounded-full h-2">
            <motion.div
              className="bg-primary h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${((currentIndex + 1) / flashcards.length) * 100}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
        </motion.div>
      )}

      {/* Flashcard */}
      <div className="relative h-96 mb-6 perspective-1000">
        <AnimatePresence mode="wait" custom={1}>
          <motion.div
            key={currentIndex}
            custom={1}
            variants={cardVariants}
            initial="enter"
            animate="center"
            exit="exit"
            className="absolute inset-0"
          >
            <motion.div
              className="relative w-full h-full preserve-3d cursor-pointer"
              animate={isFlipped ? "back" : "front"}
              variants={flipVariants}
              onClick={flipCard}
            >
              {/* Front of card */}
              <motion.div
                className="absolute inset-0 w-full h-full backface-hidden"
                style={{ backfaceVisibility: 'hidden' }}
              >
                <div className="card bg-gradient-to-br from-primary to-secondary text-primary-content h-full shadow-xl">
                  <div className="card-body flex items-center justify-center text-center">
                    <div className="mb-4">
                      <div className="badge badge-outline badge-lg mb-2">
                        {currentCard?.category || 'Question'}
                      </div>
                    </div>
                    <h2 className="card-title text-2xl mb-4 text-center">
                      {currentCard?.question || currentCard?.front}
                    </h2>
                    <p className="text-primary-content/80 text-sm">
                      Click to reveal answer
                    </p>
                  </div>
                </div>
              </motion.div>

              {/* Back of card */}
              <motion.div
                className="absolute inset-0 w-full h-full backface-hidden"
                style={{ 
                  backfaceVisibility: 'hidden',
                  transform: 'rotateY(180deg)'
                }}
              >
                <div className="card bg-gradient-to-br from-secondary to-accent text-secondary-content h-full shadow-xl">
                  <div className="card-body flex items-center justify-center text-center">
                    <div className="mb-4">
                      <div className="badge badge-outline badge-lg mb-2">
                        Answer
                      </div>
                    </div>
                    <h2 className="card-title text-xl mb-4 text-center">
                      {currentCard?.answer || currentCard?.back}
                    </h2>
                    {currentCard?.explanation && (
                      <p className="text-secondary-content/80 text-sm">
                        {currentCard.explanation}
                      </p>
                    )}
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Controls */}
      <motion.div
        className="flex items-center justify-between mb-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <motion.button
          className="btn btn-outline"
          onClick={prevCard}
          disabled={currentIndex === 0}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Previous
        </motion.button>

        <div className="flex items-center space-x-2">
          <motion.button
            className={`btn btn-circle ${isFlipped ? 'btn-success' : 'btn-primary'}`}
            onClick={flipCard}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </motion.button>

          <motion.button
            className={`btn btn-circle ${isAutoPlaying ? 'btn-warning' : 'btn-ghost'}`}
            onClick={toggleAutoPlay}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            {isAutoPlaying ? (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            ) : (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M9 10v4a2 2 0 002 2h2a2 2 0 002-2v-4M9 10V9a2 2 0 012-2h2a2 2 0 012 2v1" />
              </svg>
            )}
          </motion.button>

          <motion.button
            className="btn btn-ghost btn-circle"
            onClick={resetSession}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </motion.button>
        </div>

        <motion.button
          className="btn btn-primary"
          onClick={nextCard}
          disabled={currentIndex === flashcards.length - 1}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          Next
          <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </motion.button>
      </motion.div>

      {/* Card Navigation */}
      <motion.div
        className="flex flex-wrap justify-center gap-2"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        {flashcards.map((_, index) => (
          <motion.button
            key={index}
            className={`btn btn-xs ${
              index === currentIndex 
                ? 'btn-primary' 
                : studiedCards.has(index)
                ? 'btn-success'
                : 'btn-outline'
            }`}
            onClick={() => goToCard(index)}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            {index + 1}
          </motion.button>
        ))}
      </motion.div>
    </div>
  )
}

export default FlashcardViewer
