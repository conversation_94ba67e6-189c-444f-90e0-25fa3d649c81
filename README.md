# 🎓 Learning Management System (LMS) Frontend

A modern, full-featured Learning Management System frontend built with React, featuring role-based authentication, beautiful animations, and a comprehensive UI/UX design.

## ✨ Features

### 🔐 Authentication & Authorization
- **Multi-role system**: Admin, Professor, and Student roles
- **Secure authentication** with JWT tokens
- **Role-based routing** and access control
- **Protected routes** with automatic redirects

### 🎨 Modern UI/UX
- **Tailwind CSS** for utility-first styling
- **DaisyUI** for elegant, pre-built components
- **Framer Motion** for smooth page transitions and animations
- **GSAP** for advanced animations and scroll effects
- **Vanta.js** for dynamic 3D backgrounds
- **Lottie animations** for interactive elements
- **Dark/Light mode** with multiple theme options
- **Fully responsive** design for all devices

### 👑 Admin Dashboard
- **User management** with search and filtering
- **System analytics** with interactive charts
- **AI-powered summaries** (mocked Gemini integration)
- **Real-time system monitoring**
- **Comprehensive reporting tools**

### 👨‍🏫 Professor <PERSON>
- **Course management** and creation
- **Quiz builder** with AI assistance
- **Student analytics** and performance tracking
- **Content upload** and organization
- **Score prediction** using AI insights
- **Gradebook** and assessment tools

### 👨‍🎓 Student Dashboard
- **Course browsing** and enrollment
- **Interactive quiz taking**
- **Progress tracking** with visual charts
- **Personalized recommendations**
- **Flashcard study system**
- **Achievement tracking**

### 📊 Data Visualization
- **Recharts integration** for beautiful charts
- **Performance analytics** with multiple chart types
- **Progress tracking** with animated progress bars
- **Score distribution** and trend analysis

## 🚀 Tech Stack

### Core Technologies
- **React 19** - Latest React with functional components and hooks
- **React Router v6** - Modern routing with nested routes
- **Vite** - Fast build tool and development server
- **JavaScript (ES6+)** - Modern JavaScript features

### Styling & UI
- **Tailwind CSS 4** - Utility-first CSS framework
- **DaisyUI** - Component library built on Tailwind
- **Framer Motion** - Production-ready motion library
- **GSAP** - Professional-grade animation library
- **Vanta.js** - 3D animated backgrounds
- **Lottie React** - Render After Effects animations

### Data & Charts
- **Recharts** - Composable charting library
- **Axios** - HTTP client for API requests
- **Context API** - State management

### Development Tools
- **ESLint** - Code linting and formatting
- **Vite** - Fast development and build tool

## 📁 Project Structure

```
src/
├── assets/
│   └── animations/          # Lottie animation files
├── components/
│   ├── AnimatedBackground.jsx    # 3D backgrounds with Vanta.js
│   ├── FlashcardViewer.jsx      # Interactive flashcard system
│   ├── LottieLoader.jsx         # Loading animations
│   ├── Navbar.jsx               # Dynamic navigation bar
│   ├── QuizCard.jsx             # Quiz display components
│   ├── ScoreChart.jsx           # Chart components with Recharts
│   └── Sidebar.jsx              # Role-based sidebar navigation
├── contexts/
│   ├── AuthContext.jsx          # Authentication state management
│   └── ThemeContext.jsx         # Theme and dark mode management
├── hooks/
│   └── useApi.js               # Custom API hooks
├── pages/
│   ├── AdminDashboard.jsx       # Admin interface
│   ├── Login.jsx                # Login page with animations
│   ├── NotFound.jsx             # 404 error page
│   ├── ProfessorDashboard.jsx   # Professor interface
│   ├── Register.jsx             # Multi-step registration
│   └── StudentDashboard.jsx     # Student interface
├── services/
│   └── api.js                  # API service layer with mock data
├── utils/
│   └── auth.js                 # Authentication utilities
├── App.jsx                     # Main app component
├── main.jsx                    # App entry point
└── router.jsx                  # Route configuration
```

## 🎯 Key Components

### Authentication System
- **Login/Register** pages with smooth animations
- **Role-based redirects** after authentication
- **Token management** with automatic refresh
- **Protected routes** with role validation

### Dashboard Components
- **Animated cards** with hover effects
- **Interactive charts** showing performance data
- **Real-time notifications** and updates
- **Responsive grid layouts** for all screen sizes

### Quiz System
- **Interactive quiz cards** with difficulty indicators
- **Flashcard viewer** with flip animations
- **Progress tracking** with visual feedback
- **Score visualization** with animated charts

### Theme System
- **Multiple themes** including dark mode
- **Smooth theme transitions** with animations
- **System preference detection**
- **Persistent theme storage**

## 🛠️ Installation & Setup

### Prerequisites
- Node.js 18+
- npm or yarn package manager

### Installation Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd lms-frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Build for production**
   ```bash
   npm run build
   ```

5. **Preview production build**
   ```bash
   npm run preview
   ```

## 🔑 Demo Accounts

The application includes demo accounts for testing:

| Role | Email | Password |
|------|-------|----------|
| Admin | <EMAIL> | admin123 |
| Professor | <EMAIL> | prof123 |
| Student | <EMAIL> | student123 |

## 🎨 Customization

### Themes
The app supports multiple themes through DaisyUI:
- Light/Dark modes
- Cupcake, Corporate, Synthwave
- Retro, Cyberpunk, Valentine
- And many more!

### Animations
- **Framer Motion** for component animations
- **GSAP** for scroll-triggered animations
- **Vanta.js** for 3D background effects
- **Lottie** for micro-interactions

### Colors & Styling
Customize the design by modifying:
- `tailwind.config.js` - Tailwind configuration
- `src/index.css` - Global styles and CSS variables
- DaisyUI theme variables

## 📱 Responsive Design

The application is fully responsive with:
- **Mobile-first** approach
- **Breakpoint-specific** layouts
- **Touch-friendly** interactions
- **Optimized performance** on all devices

## 🔧 Configuration

### Environment Variables
Create a `.env` file in the root directory:

```env
VITE_API_BASE_URL=http://localhost:3001/api
VITE_APP_NAME=Learning Management System
```

### API Integration
The app uses a mock API service. To connect to a real backend:
1. Update `src/services/api.js`
2. Configure the base URL in environment variables
3. Implement proper error handling

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Deploy to Vercel
```bash
npm install -g vercel
vercel --prod
```

### Deploy to Netlify
```bash
npm run build
# Upload dist/ folder to Netlify
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **React Team** for the amazing framework
- **Tailwind CSS** for the utility-first approach
- **DaisyUI** for beautiful components
- **Framer Motion** for smooth animations
- **Recharts** for data visualization
- **Vanta.js** for 3D backgrounds

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Contact the development team
- Check the documentation

---

**Built with ❤️ using React, Tailwind CSS, and modern web technologies**
