import axios from 'axios'
import { getToken, logout, isTokenExpired } from '../utils/auth'

// Base API configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = getToken()
    if (token && !isTokenExpired(token)) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      logout()
    }
    return Promise.reject(error)
  }
)

// Mock data for development
const mockUsers = [
  {
    id: 1,
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    firstName: 'Admin',
    lastName: 'User',
    avatar: null,
  },
  {
    id: 2,
    email: '<EMAIL>',
    password: 'prof123',
    role: 'professor',
    firstName: '<PERSON>',
    lastName: 'Professor',
    avatar: null,
  },
  {
    id: 3,
    email: '<EMAIL>',
    password: 'student123',
    role: 'student',
    firstName: 'Jane',
    lastName: 'Student',
    avatar: null,
  },
]

const mockCourses = [
  {
    id: 1,
    title: 'Introduction to React',
    description: 'Learn the basics of React development',
    instructor: 'John Professor',
    duration: '8 weeks',
    level: 'Beginner',
    enrolled: 45,
    rating: 4.8,
    image: 'https://via.placeholder.com/300x200?text=React+Course',
  },
  {
    id: 2,
    title: 'Advanced JavaScript',
    description: 'Master advanced JavaScript concepts',
    instructor: 'John Professor',
    duration: '10 weeks',
    level: 'Advanced',
    enrolled: 32,
    rating: 4.9,
    image: 'https://via.placeholder.com/300x200?text=JavaScript+Course',
  },
]

const mockQuizzes = [
  {
    id: 1,
    title: 'React Fundamentals Quiz',
    courseId: 1,
    questions: 10,
    duration: 30,
    difficulty: 'Easy',
    attempts: 3,
  },
  {
    id: 2,
    title: 'JavaScript ES6+ Quiz',
    courseId: 2,
    questions: 15,
    duration: 45,
    difficulty: 'Hard',
    attempts: 2,
  },
]

// Mock API delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

// Authentication API
export const authAPI = {
  login: async (credentials) => {
    await delay(1000) // Simulate network delay
    
    const user = mockUsers.find(
      u => u.email === credentials.email && u.password === credentials.password
    )
    
    if (!user) {
      throw new Error('Invalid credentials')
    }
    
    // Generate mock JWT token
    const token = btoa(JSON.stringify({
      userId: user.id,
      role: user.role,
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
    }))
    
    return {
      token,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        firstName: user.firstName,
        lastName: user.lastName,
        avatar: user.avatar,
      },
    }
  },
  
  register: async (userData) => {
    await delay(1000)
    
    // Check if user already exists
    const existingUser = mockUsers.find(u => u.email === userData.email)
    if (existingUser) {
      throw new Error('User already exists')
    }
    
    const newUser = {
      id: mockUsers.length + 1,
      ...userData,
      avatar: null,
    }
    
    mockUsers.push(newUser)
    
    // Generate mock JWT token
    const token = btoa(JSON.stringify({
      userId: newUser.id,
      role: newUser.role,
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60),
    }))
    
    return {
      token,
      user: {
        id: newUser.id,
        email: newUser.email,
        role: newUser.role,
        firstName: newUser.firstName,
        lastName: newUser.lastName,
        avatar: newUser.avatar,
      },
    }
  },
  
  logout: async () => {
    await delay(500)
    return { success: true }
  },
}

// User API
export const userAPI = {
  getProfile: async () => {
    await delay(500)
    // In real app, get from token or API
    return mockUsers[0]
  },
  
  updateProfile: async (userData) => {
    await delay(1000)
    return { ...mockUsers[0], ...userData }
  },
  
  getAllUsers: async () => {
    await delay(1000)
    return mockUsers.map(user => ({
      ...user,
      password: undefined, // Don't return passwords
    }))
  },
}

// Course API
export const courseAPI = {
  getAllCourses: async () => {
    await delay(1000)
    return mockCourses
  },
  
  getCourse: async (id) => {
    await delay(500)
    return mockCourses.find(course => course.id === parseInt(id))
  },
  
  createCourse: async (courseData) => {
    await delay(1000)
    const newCourse = {
      id: mockCourses.length + 1,
      ...courseData,
      enrolled: 0,
      rating: 0,
    }
    mockCourses.push(newCourse)
    return newCourse
  },
  
  updateCourse: async (id, courseData) => {
    await delay(1000)
    const index = mockCourses.findIndex(course => course.id === parseInt(id))
    if (index !== -1) {
      mockCourses[index] = { ...mockCourses[index], ...courseData }
      return mockCourses[index]
    }
    throw new Error('Course not found')
  },
  
  deleteCourse: async (id) => {
    await delay(500)
    const index = mockCourses.findIndex(course => course.id === parseInt(id))
    if (index !== -1) {
      mockCourses.splice(index, 1)
      return { success: true }
    }
    throw new Error('Course not found')
  },
}

// Quiz API
export const quizAPI = {
  getAllQuizzes: async () => {
    await delay(1000)
    return mockQuizzes
  },
  
  getQuiz: async (id) => {
    await delay(500)
    return mockQuizzes.find(quiz => quiz.id === parseInt(id))
  },
  
  createQuiz: async (quizData) => {
    await delay(1000)
    const newQuiz = {
      id: mockQuizzes.length + 1,
      ...quizData,
    }
    mockQuizzes.push(newQuiz)
    return newQuiz
  },
  
  submitQuiz: async (quizId, answers) => {
    await delay(2000)
    // Mock score calculation
    const score = Math.floor(Math.random() * 40) + 60 // 60-100%
    return {
      score,
      totalQuestions: 10,
      correctAnswers: Math.floor((score / 100) * 10),
      passed: score >= 70,
    }
  },
}

// Analytics API
export const analyticsAPI = {
  getSystemStats: async () => {
    await delay(1000)
    return {
      totalUsers: mockUsers.length,
      totalCourses: mockCourses.length,
      totalQuizzes: mockQuizzes.length,
      activeUsers: Math.floor(mockUsers.length * 0.8),
      completionRate: 85,
      averageScore: 78,
    }
  },
  
  getUserProgress: async (userId) => {
    await delay(1000)
    return {
      coursesEnrolled: 3,
      coursesCompleted: 1,
      quizzesTaken: 5,
      averageScore: 82,
      totalStudyTime: 45, // hours
      achievements: ['First Quiz', 'Course Completed', 'High Scorer'],
    }
  },
}

export default api
