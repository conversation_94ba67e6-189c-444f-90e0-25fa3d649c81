import React from 'react'
import { motion } from 'framer-motion'

// Since we don't have actual Lottie files, we'll create animated SVG components
// In a real implementation, you would use actual Lottie JSON files

const LottieLoader = ({ 
  type = 'loading', 
  size = 'md', 
  className = '',
  message = '' 
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-16 h-16',
    lg: 'w-24 h-24',
    xl: 'w-32 h-32'
  }

  const LoadingSpinner = () => (
    <motion.div
      className={`${sizeClasses[size]} ${className}`}
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
    >
      <svg viewBox="0 0 50 50" className="w-full h-full">
        <motion.circle
          cx="25"
          cy="25"
          r="20"
          fill="none"
          stroke="currentColor"
          strokeWidth="4"
          strokeLinecap="round"
          strokeDasharray="31.416"
          animate={{ strokeDashoffset: [0, -31.416] }}
          transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
          className="text-primary"
        />
      </svg>
    </motion.div>
  )

  const SuccessCheckmark = () => (
    <motion.div
      className={`${sizeClasses[size]} ${className} text-success`}
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
    >
      <svg viewBox="0 0 50 50" className="w-full h-full">
        <motion.circle
          cx="25"
          cy="25"
          r="20"
          fill="none"
          stroke="currentColor"
          strokeWidth="3"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        />
        <motion.path
          d="M15 25l7 7 13-13"
          fill="none"
          stroke="currentColor"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        />
      </svg>
    </motion.div>
  )

  const ErrorCross = () => (
    <motion.div
      className={`${sizeClasses[size]} ${className} text-error`}
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
    >
      <svg viewBox="0 0 50 50" className="w-full h-full">
        <motion.circle
          cx="25"
          cy="25"
          r="20"
          fill="none"
          stroke="currentColor"
          strokeWidth="3"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        />
        <motion.path
          d="M18 18l14 14M32 18l-14 14"
          stroke="currentColor"
          strokeWidth="3"
          strokeLinecap="round"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        />
      </svg>
    </motion.div>
  )

  const BookAnimation = () => (
    <motion.div
      className={`${sizeClasses[size]} ${className} text-primary`}
      animate={{ 
        rotateY: [0, 180, 360],
        scale: [1, 1.1, 1]
      }}
      transition={{ 
        duration: 2, 
        repeat: Infinity, 
        ease: "easeInOut" 
      }}
    >
      <svg viewBox="0 0 50 50" className="w-full h-full">
        <motion.rect
          x="10"
          y="15"
          width="30"
          height="20"
          rx="2"
          fill="currentColor"
          opacity="0.8"
          animate={{ scaleX: [1, 0.9, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
        />
        <motion.line
          x1="25"
          y1="15"
          x2="25"
          y2="35"
          stroke="white"
          strokeWidth="1"
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 2, repeat: Infinity }}
        />
      </svg>
    </motion.div>
  )

  const QuizAnimation = () => (
    <motion.div
      className={`${sizeClasses[size]} ${className} text-secondary`}
      animate={{ 
        y: [-2, 2, -2],
        rotate: [-1, 1, -1]
      }}
      transition={{ 
        duration: 2, 
        repeat: Infinity, 
        ease: "easeInOut" 
      }}
    >
      <svg viewBox="0 0 50 50" className="w-full h-full">
        <motion.rect
          x="12"
          y="10"
          width="26"
          height="30"
          rx="2"
          fill="currentColor"
          opacity="0.9"
        />
        <motion.circle
          cx="18"
          cy="18"
          r="2"
          fill="white"
          animate={{ scale: [1, 1.2, 1] }}
          transition={{ duration: 1, repeat: Infinity, delay: 0 }}
        />
        <motion.circle
          cx="18"
          cy="25"
          r="2"
          fill="white"
          animate={{ scale: [1, 1.2, 1] }}
          transition={{ duration: 1, repeat: Infinity, delay: 0.3 }}
        />
        <motion.circle
          cx="18"
          cy="32"
          r="2"
          fill="white"
          animate={{ scale: [1, 1.2, 1] }}
          transition={{ duration: 1, repeat: Infinity, delay: 0.6 }}
        />
      </svg>
    </motion.div>
  )

  const CelebrationAnimation = () => (
    <motion.div
      className={`${sizeClasses[size]} ${className} text-warning`}
      animate={{ 
        scale: [1, 1.2, 1],
        rotate: [0, 10, -10, 0]
      }}
      transition={{ 
        duration: 1, 
        repeat: Infinity, 
        ease: "easeInOut" 
      }}
    >
      <svg viewBox="0 0 50 50" className="w-full h-full">
        {/* Star shape */}
        <motion.path
          d="M25 5 L30 20 L45 20 L35 30 L40 45 L25 35 L10 45 L15 30 L5 20 L20 20 Z"
          fill="currentColor"
          animate={{ 
            scale: [1, 1.1, 1],
            opacity: [0.8, 1, 0.8]
          }}
          transition={{ duration: 1, repeat: Infinity }}
        />
        {/* Sparkles */}
        {[...Array(6)].map((_, i) => (
          <motion.circle
            key={i}
            cx={15 + (i * 4)}
            cy={10 + (i % 2) * 30}
            r="1"
            fill="currentColor"
            animate={{ 
              opacity: [0, 1, 0],
              scale: [0, 1, 0]
            }}
            transition={{ 
              duration: 1.5, 
              repeat: Infinity, 
              delay: i * 0.2 
            }}
          />
        ))}
      </svg>
    </motion.div>
  )

  const renderAnimation = () => {
    switch (type) {
      case 'loading':
        return <LoadingSpinner />
      case 'success':
        return <SuccessCheckmark />
      case 'error':
        return <ErrorCross />
      case 'book':
        return <BookAnimation />
      case 'quiz':
        return <QuizAnimation />
      case 'celebration':
        return <CelebrationAnimation />
      default:
        return <LoadingSpinner />
    }
  }

  return (
    <div className="flex flex-col items-center justify-center space-y-4">
      {renderAnimation()}
      {message && (
        <motion.p
          className="text-center text-base-content/70 text-sm"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          {message}
        </motion.p>
      )}
    </div>
  )
}

// Full-screen loading overlay component
export const LoadingOverlay = ({ 
  isVisible, 
  type = 'loading', 
  message = 'Loading...',
  onClose 
}) => {
  if (!isVisible) return null

  return (
    <motion.div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={onClose}
    >
      <motion.div
        className="bg-base-100 rounded-2xl p-8 shadow-2xl border border-base-300"
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        onClick={(e) => e.stopPropagation()}
      >
        <LottieLoader type={type} size="xl" message={message} />
      </motion.div>
    </motion.div>
  )
}

// Inline loading component for cards and sections
export const InlineLoader = ({ 
  type = 'loading', 
  size = 'md', 
  message = '',
  className = '' 
}) => {
  return (
    <div className={`flex items-center justify-center p-8 ${className}`}>
      <LottieLoader type={type} size={size} message={message} />
    </div>
  )
}

// Success/Error toast-style notification
export const AnimatedToast = ({ 
  type = 'success', 
  message = '', 
  isVisible = false,
  onClose,
  duration = 3000 
}) => {
  React.useEffect(() => {
    if (isVisible && duration > 0) {
      const timer = setTimeout(onClose, duration)
      return () => clearTimeout(timer)
    }
  }, [isVisible, duration, onClose])

  if (!isVisible) return null

  return (
    <motion.div
      className="fixed top-4 right-4 z-50"
      initial={{ opacity: 0, x: 100, scale: 0.8 }}
      animate={{ opacity: 1, x: 0, scale: 1 }}
      exit={{ opacity: 0, x: 100, scale: 0.8 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      <div className={`alert ${
        type === 'success' ? 'alert-success' : 
        type === 'error' ? 'alert-error' : 
        type === 'warning' ? 'alert-warning' : 'alert-info'
      } shadow-lg`}>
        <LottieLoader type={type} size="sm" />
        <span>{message}</span>
        <button 
          className="btn btn-ghost btn-sm btn-circle"
          onClick={onClose}
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </motion.div>
  )
}

export default LottieLoader
