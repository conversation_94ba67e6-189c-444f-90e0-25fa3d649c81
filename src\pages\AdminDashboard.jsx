import React, { useState, useEffect } from 'react'
import { Routes, Route } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import Navbar from '../components/Navbar'
import Sidebar from '../components/Sidebar'
import Score<PERSON>hart from '../components/ScoreChart'
import AnimatedBackground from '../components/AnimatedBackground'

const AdminDashboard = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [stats, setStats] = useState({
    totalUsers: 1247,
    totalCourses: 89,
    totalQuizzes: 234,
    activeUsers: 892,
    systemHealth: 98.5
  })
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)

  const { user } = useAuth()

  useEffect(() => {
    // Simulate loading data
    setTimeout(() => {
      setUsers([
        { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'student', status: 'active', lastLogin: '2024-01-15' },
        { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'professor', status: 'active', lastLogin: '2024-01-14' },
        { id: 3, name: '<PERSON> <PERSON>', email: '<EMAIL>', role: 'student', status: 'inactive', lastLogin: '2024-01-10' },
        { id: 4, name: '<PERSON> <PERSON>', email: '<EMAIL>', role: 'professor', status: 'active', lastLogin: '2024-01-15' },
        { id: 5, name: 'Charlie <PERSON>', email: '<EMAIL>', role: 'student', status: 'active', lastLogin: '2024-01-15' }
      ])
      setLoading(false)
    }, 1000)
  }, [])

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    }
  }

  // Mock data for charts
  const userGrowthData = [
    { name: 'Jan', students: 400, professors: 24, admins: 5 },
    { name: 'Feb', students: 450, professors: 28, admins: 5 },
    { name: 'Mar', students: 520, professors: 32, admins: 6 },
    { name: 'Apr', students: 580, professors: 35, admins: 6 },
    { name: 'May', students: 650, professors: 38, admins: 7 },
    { name: 'Jun', students: 720, professors: 42, admins: 7 }
  ]

  const systemUsageData = [
    { name: 'Courses', value: 89, color: '#3b82f6' },
    { name: 'Quizzes', value: 234, color: '#8b5cf6' },
    { name: 'Users', value: 1247, color: '#06b6d4' },
    { name: 'Active Sessions', value: 156, color: '#10b981' }
  ]

  const MainDashboard = () => (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Welcome Section */}
      <motion.div variants={itemVariants}>
        <div className="hero bg-gradient-to-r from-primary to-secondary text-primary-content rounded-2xl">
          <div className="hero-content text-center py-12">
            <div className="max-w-md">
              <motion.h1 
                className="text-4xl font-bold mb-4"
                animate={{ 
                  backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
                }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                Welcome, {user?.firstName || 'Admin'}! 👋
              </motion.h1>
              <p className="text-lg opacity-90">
                Manage your Learning Management System from this central dashboard
              </p>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <motion.div 
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6"
        variants={containerVariants}
      >
        {[
          { title: 'Total Users', value: stats.totalUsers, icon: '👥', color: 'from-blue-500 to-blue-600', change: '+12%' },
          { title: 'Total Courses', value: stats.totalCourses, icon: '📚', color: 'from-green-500 to-green-600', change: '+8%' },
          { title: 'Total Quizzes', value: stats.totalQuizzes, icon: '📝', color: 'from-purple-500 to-purple-600', change: '+15%' },
          { title: 'Active Users', value: stats.activeUsers, icon: '🟢', color: 'from-cyan-500 to-cyan-600', change: '+5%' },
          { title: 'System Health', value: `${stats.systemHealth}%`, icon: '⚡', color: 'from-orange-500 to-orange-600', change: '+2%' }
        ].map((stat, index) => (
          <motion.div
            key={index}
            variants={itemVariants}
            whileHover={{ scale: 1.05, y: -5 }}
            className="card bg-base-100 shadow-lg border border-base-300 overflow-hidden"
          >
            <div className={`h-2 bg-gradient-to-r ${stat.color}`}></div>
            <div className="card-body p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-base-content/60 text-sm font-medium">{stat.title}</p>
                  <p className="text-2xl font-bold">{stat.value}</p>
                  <div className="flex items-center mt-1">
                    <span className="text-success text-sm font-medium">{stat.change}</span>
                    <span className="text-base-content/60 text-xs ml-1">vs last month</span>
                  </div>
                </div>
                <div className="text-3xl">{stat.icon}</div>
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div variants={itemVariants}>
          <ScoreChart
            data={userGrowthData}
            type="bar"
            title="User Growth Trends"
            height={350}
          />
        </motion.div>
        <motion.div variants={itemVariants}>
          <ScoreChart
            data={systemUsageData}
            type="pie"
            title="System Usage Distribution"
            height={350}
          />
        </motion.div>
      </div>

      {/* AI Summary Section (Mocked) */}
      <motion.div variants={itemVariants}>
        <div className="card bg-gradient-to-br from-base-100 to-base-200 shadow-lg border border-base-300">
          <div className="card-body">
            <div className="flex items-center mb-4">
              <div className="avatar">
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                  <span className="text-white font-bold">AI</span>
                </div>
              </div>
              <div className="ml-3">
                <h3 className="card-title">AI-Powered System Summary</h3>
                <p className="text-sm text-base-content/60">Generated by Gemini AI</p>
              </div>
              <div className="ml-auto">
                <motion.div
                  className="w-3 h-3 bg-green-500 rounded-full"
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                />
              </div>
            </div>
            <div className="bg-base-100 rounded-lg p-4 border border-base-300">
              <motion.p 
                className="text-base-content/80 leading-relaxed"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5, duration: 1 }}
              >
                📊 <strong>System Performance:</strong> Your LMS is performing excellently with 98.5% uptime. 
                User engagement has increased by 15% this month, with particularly strong growth in quiz completions.
                <br /><br />
                🎯 <strong>Key Insights:</strong> Students are most active between 2-4 PM and 7-9 PM. 
                The Computer Science department shows the highest engagement rates, while Mathematics courses 
                have the best completion rates at 87%.
                <br /><br />
                💡 <strong>Recommendations:</strong> Consider expanding quiz offerings during peak hours and 
                implementing gamification features to boost engagement in underperforming subjects.
              </motion.p>
            </div>
            <div className="card-actions justify-end mt-4">
              <motion.button 
                className="btn btn-primary btn-sm"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Generate New Summary
              </motion.button>
              <motion.button 
                className="btn btn-outline btn-sm"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Export Report
              </motion.button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Recent Activity */}
      <motion.div variants={itemVariants}>
        <div className="card bg-base-100 shadow-lg border border-base-300">
          <div className="card-body">
            <h3 className="card-title mb-4">Recent System Activity</h3>
            <div className="space-y-3">
              {[
                { action: 'New user registered', user: '<EMAIL>', time: '2 minutes ago', type: 'user' },
                { action: 'Quiz completed', user: '<EMAIL>', time: '5 minutes ago', type: 'quiz' },
                { action: 'Course created', user: '<EMAIL>', time: '10 minutes ago', type: 'course' },
                { action: 'System backup completed', user: 'System', time: '1 hour ago', type: 'system' }
              ].map((activity, index) => (
                <motion.div
                  key={index}
                  className="flex items-center p-3 bg-base-200 rounded-lg"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div className={`w-3 h-3 rounded-full mr-3 ${
                    activity.type === 'user' ? 'bg-blue-500' :
                    activity.type === 'quiz' ? 'bg-green-500' :
                    activity.type === 'course' ? 'bg-purple-500' : 'bg-orange-500'
                  }`} />
                  <div className="flex-1">
                    <p className="font-medium">{activity.action}</p>
                    <p className="text-sm text-base-content/60">{activity.user}</p>
                  </div>
                  <span className="text-xs text-base-content/60">{activity.time}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  )

  return (
    <div className="min-h-screen bg-base-100">
      <Navbar onSidebarToggle={() => setSidebarOpen(!sidebarOpen)} />
      
      <div className="flex">
        <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />
        
        <main className="flex-1 p-6 lg:ml-0">
          <AnimatedBackground type="particles" className="fixed inset-0 -z-10" />
          
          <Routes>
            <Route path="/" element={<MainDashboard />} />
            <Route path="/users" element={<div>User Management (Coming Soon)</div>} />
            <Route path="/analytics" element={<div>System Analytics (Coming Soon)</div>} />
            <Route path="/settings" element={<div>System Settings (Coming Soon)</div>} />
          </Routes>
        </main>
      </div>
    </div>
  )
}

export default AdminDashboard
