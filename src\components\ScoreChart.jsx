import React, { useState } from 'react'
import { motion } from 'framer-motion'
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart,
  RadialBarChart,
  RadialBar
} from 'recharts'

const ScoreChart = ({ 
  data = [], 
  type = 'bar', 
  title = 'Score Analysis',
  className = '',
  height = 300,
  showLegend = true,
  animated = true
}) => {
  const [activeIndex, setActiveIndex] = useState(0)

  // Color palette for charts
  const colors = [
    '#3b82f6', // blue
    '#8b5cf6', // purple
    '#06b6d4', // cyan
    '#10b981', // emerald
    '#f59e0b', // amber
    '#ef4444', // red
    '#6366f1', // indigo
    '#ec4899'  // pink
  ]

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  const chartVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: { 
      scale: 1, 
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
        delay: 0.2
      }
    }
  }

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <motion.div
          className="bg-base-100 p-3 rounded-lg shadow-lg border border-base-300"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.2 }}
        >
          <p className="font-semibold text-base-content">{label}</p>
          {payload.map((entry, index) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {entry.name}: {entry.value}
              {entry.name.toLowerCase().includes('score') ? '%' : ''}
            </p>
          ))}
        </motion.div>
      )
    }
    return null
  }

  // Render different chart types
  const renderChart = () => {
    switch (type) {
      case 'pie':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                animationBegin={animated ? 0 : undefined}
                animationDuration={animated ? 800 : 0}
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        )

      case 'line':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <LineChart data={data}>
              <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--bc) / 0.1)" />
              <XAxis 
                dataKey="name" 
                stroke="hsl(var(--bc) / 0.6)"
                fontSize={12}
              />
              <YAxis 
                stroke="hsl(var(--bc) / 0.6)"
                fontSize={12}
              />
              <Tooltip content={<CustomTooltip />} />
              {showLegend && <Legend />}
              <Line 
                type="monotone" 
                dataKey="score" 
                stroke={colors[0]} 
                strokeWidth={3}
                dot={{ fill: colors[0], strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: colors[0], strokeWidth: 2 }}
                animationDuration={animated ? 1000 : 0}
              />
            </LineChart>
          </ResponsiveContainer>
        )

      case 'area':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <AreaChart data={data}>
              <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--bc) / 0.1)" />
              <XAxis 
                dataKey="name" 
                stroke="hsl(var(--bc) / 0.6)"
                fontSize={12}
              />
              <YAxis 
                stroke="hsl(var(--bc) / 0.6)"
                fontSize={12}
              />
              <Tooltip content={<CustomTooltip />} />
              {showLegend && <Legend />}
              <Area 
                type="monotone" 
                dataKey="score" 
                stroke={colors[0]} 
                fill={`${colors[0]}40`}
                strokeWidth={2}
                animationDuration={animated ? 1000 : 0}
              />
            </AreaChart>
          </ResponsiveContainer>
        )

      case 'radial':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <RadialBarChart cx="50%" cy="50%" innerRadius="10%" outerRadius="80%" data={data}>
              <RadialBar 
                dataKey="score" 
                cornerRadius={10} 
                fill={colors[0]}
                animationDuration={animated ? 1000 : 0}
              />
              <Tooltip content={<CustomTooltip />} />
            </RadialBarChart>
          </ResponsiveContainer>
        )

      case 'bar':
      default:
        return (
          <ResponsiveContainer width="100%" height={height}>
            <BarChart data={data}>
              <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--bc) / 0.1)" />
              <XAxis 
                dataKey="name" 
                stroke="hsl(var(--bc) / 0.6)"
                fontSize={12}
              />
              <YAxis 
                stroke="hsl(var(--bc) / 0.6)"
                fontSize={12}
              />
              <Tooltip content={<CustomTooltip />} />
              {showLegend && <Legend />}
              <Bar 
                dataKey="score" 
                fill={colors[0]}
                radius={[4, 4, 0, 0]}
                animationDuration={animated ? 800 : 0}
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        )
    }
  }

  return (
    <motion.div
      className={`card bg-base-100 shadow-lg border border-base-300 ${className}`}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="card-body">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h3 className="card-title text-lg font-bold">{title}</h3>
          <div className="dropdown dropdown-end">
            <label tabIndex={0} className="btn btn-ghost btn-sm">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
              </svg>
            </label>
            <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
              <li><a>Export as PNG</a></li>
              <li><a>Export as PDF</a></li>
              <li><a>View Full Screen</a></li>
            </ul>
          </div>
        </div>

        {/* Chart */}
        <motion.div
          variants={chartVariants}
          initial="hidden"
          animate="visible"
        >
          {data.length > 0 ? (
            renderChart()
          ) : (
            <div className="flex items-center justify-center h-64 text-base-content/60">
              <div className="text-center">
                <div className="text-4xl mb-2">📊</div>
                <p>No data available</p>
              </div>
            </div>
          )}
        </motion.div>

        {/* Stats Summary */}
        {data.length > 0 && (
          <motion.div
            className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4 pt-4 border-t border-base-300"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <div className="stat">
              <div className="stat-title text-xs">Average</div>
              <div className="stat-value text-sm">
                {(data.reduce((sum, item) => sum + (item.score || item.value || 0), 0) / data.length).toFixed(1)}%
              </div>
            </div>
            <div className="stat">
              <div className="stat-title text-xs">Highest</div>
              <div className="stat-value text-sm">
                {Math.max(...data.map(item => item.score || item.value || 0))}%
              </div>
            </div>
            <div className="stat">
              <div className="stat-title text-xs">Lowest</div>
              <div className="stat-value text-sm">
                {Math.min(...data.map(item => item.score || item.value || 0))}%
              </div>
            </div>
            <div className="stat">
              <div className="stat-title text-xs">Total</div>
              <div className="stat-value text-sm">{data.length}</div>
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  )
}

// Preset chart configurations
export const ChartPresets = {
  studentProgress: {
    type: 'line',
    title: 'Learning Progress',
    height: 300
  },
  quizScores: {
    type: 'bar',
    title: 'Quiz Scores',
    height: 250
  },
  subjectDistribution: {
    type: 'pie',
    title: 'Subject Distribution',
    height: 300
  },
  performanceOverview: {
    type: 'area',
    title: 'Performance Overview',
    height: 350
  },
  skillLevel: {
    type: 'radial',
    title: 'Skill Level',
    height: 250
  }
}

export default ScoreChart
