import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { useAuth } from '../contexts/AuthContext'

const QuizCard = ({ 
  quiz, 
  onStart, 
  onEdit, 
  onDelete, 
  onViewResults,
  showActions = true,
  className = '' 
}) => {
  const { user, hasRole } = useAuth()
  const [isHovered, setIsHovered] = useState(false)

  const cardVariants = {
    hidden: { 
      opacity: 0, 
      y: 20,
      scale: 0.95
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    hover: {
      y: -5,
      scale: 1.02,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 25
      }
    }
  }

  const iconVariants = {
    hidden: { scale: 0, rotate: -180 },
    visible: { 
      scale: 1, 
      rotate: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 20,
        delay: 0.2
      }
    }
  }

  const getDifficultyColor = (difficulty) => {
    switch (difficulty?.toLowerCase()) {
      case 'easy':
        return 'badge-success'
      case 'medium':
        return 'badge-warning'
      case 'hard':
        return 'badge-error'
      default:
        return 'badge-neutral'
    }
  }

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return 'badge-success'
      case 'in-progress':
        return 'badge-warning'
      case 'not-started':
        return 'badge-neutral'
      default:
        return 'badge-neutral'
    }
  }

  const formatDuration = (minutes) => {
    if (minutes < 60) {
      return `${minutes} min`
    }
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return `${hours}h ${remainingMinutes}m`
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <motion.div
      className={`card bg-base-100 shadow-lg hover:shadow-xl border border-base-300 cursor-pointer ${className}`}
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      whileHover="hover"
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <div className="card-body p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <motion.h3 
              className="card-title text-lg font-bold mb-2"
              animate={{ color: isHovered ? '#3b82f6' : 'inherit' }}
            >
              {quiz.title}
            </motion.h3>
            <p className="text-base-content/70 text-sm line-clamp-2">
              {quiz.description}
            </p>
          </div>
          
          <motion.div
            className="flex-shrink-0 ml-4"
            variants={iconVariants}
          >
            <div className="avatar placeholder">
              <div className="bg-gradient-to-r from-primary to-secondary text-primary-content rounded-full w-12 h-12">
                <span className="text-lg font-bold">
                  {quiz.subject?.charAt(0).toUpperCase() || 'Q'}
                </span>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Quiz Stats */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="stat bg-base-200 rounded-lg p-3">
            <div className="stat-title text-xs">Questions</div>
            <div className="stat-value text-lg">{quiz.questionCount || 0}</div>
          </div>
          <div className="stat bg-base-200 rounded-lg p-3">
            <div className="stat-title text-xs">Duration</div>
            <div className="stat-value text-lg">{formatDuration(quiz.duration || 30)}</div>
          </div>
        </div>

        {/* Badges */}
        <div className="flex flex-wrap gap-2 mb-4">
          <span className={`badge ${getDifficultyColor(quiz.difficulty)}`}>
            {quiz.difficulty || 'Medium'}
          </span>
          <span className="badge badge-outline">
            {quiz.subject || 'General'}
          </span>
          {quiz.status && (
            <span className={`badge ${getStatusColor(quiz.status)}`}>
              {quiz.status}
            </span>
          )}
          {quiz.score !== undefined && (
            <span className="badge badge-primary">
              Score: {quiz.score}%
            </span>
          )}
        </div>

        {/* Additional Info */}
        <div className="text-xs text-base-content/60 mb-4">
          {quiz.createdBy && (
            <p>Created by: {quiz.createdBy}</p>
          )}
          {quiz.createdAt && (
            <p>Created: {formatDate(quiz.createdAt)}</p>
          )}
          {quiz.attempts !== undefined && (
            <p>Attempts: {quiz.attempts}</p>
          )}
        </div>

        {/* Actions */}
        {showActions && (
          <motion.div 
            className="card-actions justify-end"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            {hasRole('student') && (
              <motion.button
                className="btn btn-primary btn-sm"
                onClick={(e) => {
                  e.stopPropagation()
                  onStart?.(quiz)
                }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {quiz.status === 'completed' ? 'Retake' : 'Start Quiz'}
              </motion.button>
            )}

            {hasRole('professor') && (
              <>
                <motion.button
                  className="btn btn-outline btn-sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    onEdit?.(quiz)
                  }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Edit
                </motion.button>
                <motion.button
                  className="btn btn-info btn-sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    onViewResults?.(quiz)
                  }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Results
                </motion.button>
              </>
            )}

            {hasRole('admin') && (
              <motion.button
                className="btn btn-error btn-sm"
                onClick={(e) => {
                  e.stopPropagation()
                  onDelete?.(quiz)
                }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Delete
              </motion.button>
            )}
          </motion.div>
        )}
      </div>

      {/* Hover overlay effect */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-2xl"
        initial={{ opacity: 0 }}
        animate={{ opacity: isHovered ? 1 : 0 }}
        transition={{ duration: 0.3 }}
      />
    </motion.div>
  )
}

// Quiz Grid Component for displaying multiple quiz cards
export const QuizGrid = ({ 
  quizzes = [], 
  onStart, 
  onEdit, 
  onDelete, 
  onViewResults,
  loading = false,
  emptyMessage = "No quizzes available",
  className = ""
}) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  if (loading) {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
        {[...Array(6)].map((_, index) => (
          <div key={index} className="card bg-base-100 shadow-lg animate-pulse">
            <div className="card-body">
              <div className="h-4 bg-base-300 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-base-300 rounded w-full mb-4"></div>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="h-16 bg-base-300 rounded"></div>
                <div className="h-16 bg-base-300 rounded"></div>
              </div>
              <div className="flex gap-2 mb-4">
                <div className="h-6 bg-base-300 rounded w-16"></div>
                <div className="h-6 bg-base-300 rounded w-20"></div>
              </div>
              <div className="flex justify-end">
                <div className="h-8 bg-base-300 rounded w-20"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (quizzes.length === 0) {
    return (
      <motion.div
        className="text-center py-12"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="text-6xl mb-4">📝</div>
        <h3 className="text-xl font-semibold mb-2">No Quizzes Found</h3>
        <p className="text-base-content/60">{emptyMessage}</p>
      </motion.div>
    )
  }

  return (
    <motion.div
      className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {quizzes.map((quiz) => (
        <QuizCard
          key={quiz.id}
          quiz={quiz}
          onStart={onStart}
          onEdit={onEdit}
          onDelete={onDelete}
          onViewResults={onViewResults}
        />
      ))}
    </motion.div>
  )
}

export default QuizCard
